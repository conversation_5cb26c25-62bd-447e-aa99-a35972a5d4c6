# OMOP ETL Pipeline - Build Configuration Template
# This file is automatically generated from build.env.j2
# Environment: dev
# Generated at: 2025-08-05T00:48:16.990500

# Build Configuration
BUILD_TARGET=all
BUILD_TYPE=debug
ENABLE_TESTS=True
ENABLE_GRPC=True
ENABLE_REST_API=True

# Development Configuration  
DEV_BUILD_TARGET=all
DEV_BUILD_TYPE=debug
DEV_ENABLE_TESTS=True
DEV_ENABLE_GRPC=True
DEV_ENABLE_REST_API=True

# Docker Configuration
DOCKER_PLATFORM=linux/amd64
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1

# Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=clinical_db_dev
POSTGRES_USER=clinical_user_dev
POSTGRES_PASSWORD=dev_clinical_pass_123

OMOP_HOST=omop-db
OMOP_PORT=5432
OMOP_DB=omop_cdm_dev
OMOP_USER=omop_user_dev
OMOP_PASSWORD=dev_omop_pass_123

# Security Configuration
JWT_SECRET=dev-jwt-secret-key-not-for-production
ENCRYPTION_KEY=dev-encryption-key-32-chars-long

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=json
LOG_ROTATION=daily

# Paths
OMOP_CONFIG_PATH=/etc/omop-etl
OMOP_LOG_PATH=/var/log/omop-etl
OMOP_DATA_PATH=/var/lib/omop-etl/data
OMOP_BACKUP_PATH=/var/lib/omop-etl/backup

# Performance Configuration
MAX_PARALLEL_JOBS=4
MEMORY_LIMIT=8G
CPU_LIMIT=4.0

# Registry Configuration

# Feature Flags
ENABLE_MONITORING=True
ENABLE_METRICS=True
ENABLE_PROFILING=True
ENABLE_DEBUG=True

# Environment-specific overrides
development_mode=True
debug_logging=True
skip_migrations=False
enable_hot_reload=True
 