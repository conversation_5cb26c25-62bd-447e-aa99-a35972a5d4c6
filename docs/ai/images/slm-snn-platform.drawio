<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net">
  <diagram name="SLM-SNN Platform" id="platform">
    <mxGraphModel dx="2074" dy="1134" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="900">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="2" value="Integrated SLM-SNN Development Platform" style="text;html=1;align=center;verticalAlign=middle;fontSize=24;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="60" width="400" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="3" value="Visual Design Studio" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="120" width="320" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="4" value="Code Generation Engine" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#DBEAFE;strokeColor=#3B82F6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="120" width="320" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="5" value="Simulation Environment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D1FAE5;strokeColor=#10B981;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="880" y="120" width="320" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="6" value="Real-Time Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#EDE9FE;strokeColor=#8B5CF6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1280" y="120" width="240" height="280" as="geometry" />
        </mxCell>
        
        <mxCell id="7" value="Deployment Pipeline" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FED7AA;strokeColor=#F97316;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="1440" height="320" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>