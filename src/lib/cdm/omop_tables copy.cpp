/**
 * @file omop_tables.cpp
 * @brief Implementation of OMOP CDM table classes for UK healthcare data
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "omop_tables.h"
#include "table_definitions.h"
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <any>
#include <array>
#include <ctime>
#include <stdexcept>
#include <regex>
#include <cctype>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <memory>

namespace omop::cdm {

// Helper functions for OmopTable base class
std::string OmopTable::format_datetime_sql(
    const std::chrono::system_clock::time_point& time_point) {

    if (time_point == std::chrono::system_clock::time_point{}) {
        return "NULL";
    }

    // Thread-safe date formatting
    auto time_t_val = std::chrono::system_clock::to_time_t(time_point);
    std::stringstream ss;

    struct tm tm_buf;

#ifdef _WIN32
    if (localtime_s(&tm_buf, &time_t_val) != 0) {
        return "NULL";
    }
#else
    if (localtime_r(&time_t_val, &tm_buf) == nullptr) {
        return "NULL";
    }
#endif

    ss << "'" << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S") << "'";
    return ss.str();
}

std::string OmopTable::escape_string_sql(const std::string& value) {
    if (value.empty()) {
        return "NULL";
    }

    std::string escaped = value;

    // Escape single quotes by doubling them
    size_t pos = 0;
    while ((pos = escaped.find('\'', pos)) != std::string::npos) {
        escaped.replace(pos, 1, "''");
        pos += 2;
    }

    // Escape backslashes for safety
    pos = 0;
    while ((pos = escaped.find('\\', pos)) != std::string::npos) {
        escaped.replace(pos, 1, "\\\\");
        pos += 2;
    }

    return "'" + escaped + "'";
}

// UK date format helper
std::string OmopTable::format_uk_date(const std::chrono::system_clock::time_point& time_point) {
    if (time_point == std::chrono::system_clock::time_point{}) {
        return "";
    }
    auto time_t_val = std::chrono::system_clock::to_time_t(time_point);
    struct tm tm_buf;
#ifdef _WIN32
    if (localtime_s(&tm_buf, &time_t_val) != 0) {
        return "";
    }
#else
    if (localtime_r(&time_t_val, &tm_buf) == nullptr) {
        return "";
    }
#endif
    std::stringstream ss;
    ss << std::setfill('0') << std::setw(2) << tm_buf.tm_mday << "/"
       << std::setfill('0') << std::setw(2) << (tm_buf.tm_mon + 1) << "/"
       << (tm_buf.tm_year + 1900);
    return ss.str();
}

// Person implementation
std::string Person::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";

    // Build values with proper escaping
    ss << person_id << ", ";
    ss << gender_concept_id << ", ";
    ss << year_of_birth << ", ";
    ss << (month_of_birth.has_value() ? std::to_string(month_of_birth.value()) : "NULL") << ", ";
    ss << (day_of_birth.has_value() ? std::to_string(day_of_birth.value()) : "NULL") << ", ";
    ss << (birth_datetime.has_value() ? format_datetime_sql(birth_datetime.value()) : "NULL") << ", ";
    ss << race_concept_id << ", ";
    ss << ethnicity_concept_id << ", ";
    ss << (location_id.has_value() ? std::to_string(location_id.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (person_source_value.has_value() ? escape_string_sql(person_source_value.value()) : "NULL") << ", ";
        ss << (gender_source_value.has_value() ? escape_string_sql(gender_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (person_source_value.has_value() ? "'" + person_source_value.value() + "'" : "NULL") << ", ";
        ss << (gender_source_value.has_value() ? "'" + gender_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (gender_source_concept_id.has_value() ? std::to_string(gender_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (race_source_value.has_value() ? escape_string_sql(race_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (race_source_value.has_value() ? "'" + race_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (race_source_concept_id.has_value() ? std::to_string(race_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (ethnicity_source_value.has_value() ? escape_string_sql(ethnicity_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (ethnicity_source_value.has_value() ? "'" + ethnicity_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (ethnicity_source_concept_id.has_value() ? std::to_string(ethnicity_source_concept_id.value()) : "NULL");

    ss << ")";
    return ss.str();
}

std::vector<std::string> Person::field_names() const {
    return {
        "person_id", "gender_concept_id", "year_of_birth", "month_of_birth",
        "day_of_birth", "birth_datetime", "race_concept_id", "ethnicity_concept_id",
        "location_id", "provider_id", "care_site_id", "person_source_value",
        "gender_source_value", "gender_source_concept_id", "race_source_value",
        "race_source_concept_id", "ethnicity_source_value", "ethnicity_source_concept_id"
    };
}

std::vector<std::any> Person::field_values() const {
    std::vector<std::any> values;
    values.reserve(18);

    values.push_back(person_id);
    values.push_back(gender_concept_id);
    values.push_back(year_of_birth);
    values.push_back(month_of_birth.value_or(0));
    values.push_back(day_of_birth.value_or(0));
    values.push_back(birth_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(race_concept_id);
    values.push_back(ethnicity_concept_id);
    values.push_back(location_id.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(person_source_value.value_or(""));
    values.push_back(gender_source_value.value_or(""));
    values.push_back(gender_source_concept_id.value_or(0));
    values.push_back(race_source_value.value_or(""));
    values.push_back(race_source_concept_id.value_or(0));
    values.push_back(ethnicity_source_value.value_or(""));
    values.push_back(ethnicity_source_concept_id.value_or(0));

    return values;
}

void Person::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("person_id", person_id);
    visitor.visit("gender_concept_id", gender_concept_id);
    visitor.visit("year_of_birth", year_of_birth);
    visitor.visit("month_of_birth", month_of_birth);
    visitor.visit("day_of_birth", day_of_birth);
    visitor.visit("birth_datetime", birth_datetime);
    visitor.visit("race_concept_id", race_concept_id);
    visitor.visit("ethnicity_concept_id", ethnicity_concept_id);
    visitor.visit("location_id", location_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("person_source_value", person_source_value);
    visitor.visit("gender_source_value", gender_source_value);
    visitor.visit("gender_source_concept_id", gender_source_concept_id);
    visitor.visit("race_source_value", race_source_value);
    visitor.visit("race_source_concept_id", race_source_concept_id);
    visitor.visit("ethnicity_source_value", ethnicity_source_value);
    visitor.visit("ethnicity_source_concept_id", ethnicity_source_concept_id);
}

bool Person::validate() const {
    return person_id > 0 &&
           gender_concept_id > 0 &&
           year_of_birth > 0 &&
           validate_year_of_birth() &&
           validate_month_of_birth() &&
           validate_day_of_birth();
}

std::vector<std::string> Person::validation_errors() const {
    std::vector<std::string> errors;

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (gender_concept_id <= 0) {
        errors.push_back("gender_concept_id must be positive");
    }

    if (year_of_birth <= 0) {
        errors.push_back("year_of_birth must be positive");
    } else if (!validate_year_of_birth()) {
        auto now = std::chrono::system_clock::now();
        auto time_t_val = std::chrono::system_clock::to_time_t(now);
        struct tm tm_buf;
#ifdef _WIN32
        if (localtime_s(&tm_buf, &time_t_val) != 0) {
            errors.push_back("year_of_birth must be between 1850 and current year");
        } else {
            int current_year = tm_buf.tm_year + 1900;
            errors.push_back("year_of_birth must be between 1850 and " + std::to_string(current_year));
        }
#else
        if (localtime_r(&time_t_val, &tm_buf) == nullptr) {
            errors.push_back("year_of_birth must be between 1850 and current year");
        } else {
            int current_year = tm_buf.tm_year + 1900;
            errors.push_back("year_of_birth must be between 1850 and " + std::to_string(current_year));
        }
#endif
    }

    if (!validate_month_of_birth()) {
        errors.push_back("month_of_birth must be between 1 and 12");
    }

    if (!validate_day_of_birth()) {
        errors.push_back("day_of_birth must be between 1 and 31");
    }

    return errors;
}

bool Person::validate_year_of_birth() const {
    if (year_of_birth <= 0) {
        return false;
    }
    
    auto now = std::chrono::system_clock::now();
    auto time_t_val = std::chrono::system_clock::to_time_t(now);
    struct tm tm_buf;
    int current_year;
    
#ifdef _WIN32
    if (localtime_s(&tm_buf, &time_t_val) != 0) {
        current_year = 2025; // Fallback year
    } else {
        current_year = tm_buf.tm_year + 1900;
    }
#else
    if (localtime_r(&time_t_val, &tm_buf) == nullptr) {
        current_year = 2025; // Fallback year
    } else {
        current_year = tm_buf.tm_year + 1900;
    }
#endif

    // Reject future years and very old years
    return year_of_birth >= 1850 && year_of_birth <= current_year;
}

bool Person::validate_month_of_birth() const {
    if (!month_of_birth.has_value()) {
        return true; // Optional field
    }
    return month_of_birth.value() >= 1 && month_of_birth.value() <= 12;
}

bool Person::validate_day_of_birth() const {
    if (!day_of_birth.has_value()) {
        return true; // Optional field
    }

    if (day_of_birth.value() < 1 || day_of_birth.value() > 31) {
        return false;
    }

    // More sophisticated validation considering month and year
    if (month_of_birth.has_value()) {
        int month = month_of_birth.value();
        int day = day_of_birth.value();

        // Days in month (non-leap year)
        static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

        if (month >= 1 && month <= 12) {
            if (month == 2 && day == 29) {
                // Check if it's actually a leap year
                if (year_of_birth > 0) {
                    bool is_leap_year = (year_of_birth % 4 == 0 && year_of_birth % 100 != 0) || 
                                       (year_of_birth % 400 == 0);
                    return is_leap_year;
                }
                return false; // Can't validate without year
            }
            return day <= days_in_month[month - 1];
        }
    }

    return true;
}

// ObservationPeriod implementation
std::string ObservationPeriod::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << observation_period_id << ", ";
    ss << person_id << ", ";
    ss << format_datetime_sql(observation_period_start_date) << ", ";
    ss << format_datetime_sql(observation_period_end_date) << ", ";
    ss << period_type_concept_id;
    ss << ")";

    return ss.str();
}

std::vector<std::string> ObservationPeriod::field_names() const {
    return {
        "observation_period_id", "person_id", "observation_period_start_date",
        "observation_period_end_date", "period_type_concept_id"
    };
}

std::vector<std::any> ObservationPeriod::field_values() const {
    std::vector<std::any> values;
    values.reserve(5);

    values.push_back(observation_period_id);
    values.push_back(person_id);
    values.push_back(observation_period_start_date);
    values.push_back(observation_period_end_date);
    values.push_back(period_type_concept_id);

    return values;
}

void ObservationPeriod::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("observation_period_id", observation_period_id);
    visitor.visit("person_id", person_id);
    visitor.visit("observation_period_start_date", observation_period_start_date);
    visitor.visit("observation_period_end_date", observation_period_end_date);
    visitor.visit("period_type_concept_id", period_type_concept_id);
}

bool ObservationPeriod::validate() const {
    return observation_period_id > 0 &&
           person_id > 0 &&
           period_type_concept_id > 0 &&
           validate_date_range();
}

std::vector<std::string> ObservationPeriod::validation_errors() const {
    std::vector<std::string> errors;

    if (observation_period_id <= 0) {
        errors.push_back("observation_period_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (!validate_date_range()) {
        errors.push_back("observation_period_start_date must be before or equal to observation_period_end_date");
    }

    if (period_type_concept_id <= 0) {
        errors.push_back("period_type_concept_id must be positive");
    }

    return errors;
}

bool ObservationPeriod::validate_date_range() const {
    return observation_period_start_date <= observation_period_end_date;
}

// VisitOccurrence implementation
std::string VisitOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << visit_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << visit_concept_id << ", ";
    ss << format_datetime_sql(visit_start_date) << ", ";
    ss << format_datetime_sql(visit_end_date) << ", ";
    ss << visit_type_concept_id << ", ";
    ss << (visit_start_datetime.has_value() ? format_datetime_sql(visit_start_datetime.value()) : "NULL") << ", ";
    ss << (visit_end_datetime.has_value() ? format_datetime_sql(visit_end_datetime.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (visit_source_value.has_value() ? escape_string_sql(visit_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (visit_source_value.has_value() ? "'" + visit_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (visit_source_concept_id.has_value() ? std::to_string(visit_source_concept_id.value()) : "NULL") << ", ";
    ss << (admitted_from_concept_id.has_value() ? std::to_string(admitted_from_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (admitted_from_source_value.has_value() ? escape_string_sql(admitted_from_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (admitted_from_source_value.has_value() ? "'" + admitted_from_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (discharged_to_concept_id.has_value() ? std::to_string(discharged_to_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (discharged_to_source_value.has_value() ? escape_string_sql(discharged_to_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (discharged_to_source_value.has_value() ? "'" + discharged_to_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (preceding_visit_occurrence_id.has_value() ? std::to_string(preceding_visit_occurrence_id.value()) : "NULL");
    ss << ")";

    return ss.str();
}

std::vector<std::string> VisitOccurrence::field_names() const {
    return {
        "visit_occurrence_id", "person_id", "visit_concept_id", "visit_start_date",
        "visit_end_date", "visit_type_concept_id", "visit_start_datetime",
        "visit_end_datetime", "provider_id", "care_site_id", "visit_source_value",
        "visit_source_concept_id", "admitted_from_concept_id", "admitted_from_source_value",
        "discharged_to_concept_id", "discharged_to_source_value", "preceding_visit_occurrence_id"
    };
}

std::vector<std::any> VisitOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(17);

    values.push_back(visit_occurrence_id);
    values.push_back(person_id);
    values.push_back(visit_concept_id);
    values.push_back(visit_start_date);
    values.push_back(visit_end_date);
    values.push_back(visit_type_concept_id);
    values.push_back(visit_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(visit_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(provider_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(visit_source_value.value_or(""));
    values.push_back(visit_source_concept_id.value_or(0));
    values.push_back(admitted_from_concept_id.value_or(0));
    values.push_back(admitted_from_source_value.value_or(""));
    values.push_back(discharged_to_concept_id.value_or(0));
    values.push_back(discharged_to_source_value.value_or(""));
    values.push_back(preceding_visit_occurrence_id.value_or(0));

    return values;
}

void VisitOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("visit_concept_id", visit_concept_id);
    visitor.visit("visit_start_date", visit_start_date);
    visitor.visit("visit_end_date", visit_end_date);
    visitor.visit("visit_type_concept_id", visit_type_concept_id);
    visitor.visit("visit_start_datetime", visit_start_datetime);
    visitor.visit("visit_end_datetime", visit_end_datetime);
    visitor.visit("provider_id", provider_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("visit_source_value", visit_source_value);
    visitor.visit("visit_source_concept_id", visit_source_concept_id);
    visitor.visit("admitted_from_concept_id", admitted_from_concept_id);
    visitor.visit("admitted_from_source_value", admitted_from_source_value);
    visitor.visit("discharged_to_concept_id", discharged_to_concept_id);
    visitor.visit("discharged_to_source_value", discharged_to_source_value);
    visitor.visit("preceding_visit_occurrence_id", preceding_visit_occurrence_id);
}

bool VisitOccurrence::validate() const {
    return visit_occurrence_id > 0 &&
           person_id > 0 &&
           visit_concept_id > 0 &&
           visit_start_date <= visit_end_date &&
           visit_type_concept_id > 0;
}

std::vector<std::string> VisitOccurrence::validation_errors() const {
    std::vector<std::string> errors;

    if (visit_occurrence_id <= 0) {
        errors.push_back("visit_occurrence_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (visit_concept_id <= 0) {
        errors.push_back("visit_concept_id must be positive");
    }

    if (visit_start_date > visit_end_date) {
        errors.push_back("visit_start_date must be before or equal to visit_end_date");
    }

    if (visit_type_concept_id <= 0) {
        errors.push_back("visit_type_concept_id must be positive");
    }

    return errors;
}

// ConditionOccurrence implementation
std::string ConditionOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << condition_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << condition_concept_id << ", ";
    ss << format_datetime_sql(condition_start_date) << ", ";
    ss << (condition_start_datetime.has_value() ? format_datetime_sql(condition_start_datetime.value()) : "NULL") << ", ";
    ss << (condition_end_date.has_value() ? format_datetime_sql(condition_end_date.value()) : "NULL") << ", ";
    ss << (condition_end_datetime.has_value() ? format_datetime_sql(condition_end_datetime.value()) : "NULL") << ", ";
    ss << condition_type_concept_id << ", ";
    ss << (condition_status_concept_id.has_value() ? std::to_string(condition_status_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (stop_reason.has_value() ? escape_string_sql(stop_reason.value()) : "NULL") << ", ";
    } else {
        ss << (stop_reason.has_value() ? "'" + stop_reason.value() + "'" : "NULL") << ", ";
    }

    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (condition_source_value.has_value() ? escape_string_sql(condition_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (condition_source_value.has_value() ? "'" + condition_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (condition_source_concept_id.has_value() ? std::to_string(condition_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (condition_status_source_value.has_value() ? escape_string_sql(condition_status_source_value.value()) : "NULL");
    } else {
        ss << (condition_status_source_value.has_value() ? "'" + condition_status_source_value.value() + "'" : "NULL");
    }

    ss << ")";
    return ss.str();
}

std::vector<std::string> ConditionOccurrence::field_names() const {
    return {
        "condition_occurrence_id", "person_id", "condition_concept_id",
        "condition_start_date", "condition_start_datetime", "condition_end_date",
        "condition_end_datetime", "condition_type_concept_id", "condition_status_concept_id",
        "stop_reason", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "condition_source_value", "condition_source_concept_id", "condition_status_source_value"
    };
}

std::vector<std::any> ConditionOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);

    values.push_back(condition_occurrence_id);
    values.push_back(person_id);
    values.push_back(condition_concept_id);
    values.push_back(condition_start_date);
    values.push_back(condition_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_type_concept_id);
    values.push_back(condition_status_concept_id.value_or(0));
    values.push_back(stop_reason.value_or(""));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(condition_source_value.value_or(""));
    values.push_back(condition_source_concept_id.value_or(0));
    values.push_back(condition_status_source_value.value_or(""));

    return values;
}

void ConditionOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("condition_occurrence_id", condition_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("condition_concept_id", condition_concept_id);
    visitor.visit("condition_start_date", condition_start_date);
    visitor.visit("condition_start_datetime", condition_start_datetime);
    visitor.visit("condition_end_date", condition_end_date);
    visitor.visit("condition_end_datetime", condition_end_datetime);
    visitor.visit("condition_type_concept_id", condition_type_concept_id);
    visitor.visit("condition_status_concept_id", condition_status_concept_id);
    visitor.visit("stop_reason", stop_reason);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("condition_source_value", condition_source_value);
    visitor.visit("condition_source_concept_id", condition_source_concept_id);
    visitor.visit("condition_status_source_value", condition_status_source_value);
}

bool ConditionOccurrence::validate() const {
    return condition_occurrence_id > 0 &&
           person_id > 0 &&
           condition_concept_id > 0 &&
           condition_type_concept_id > 0;
}

std::vector<std::string> ConditionOccurrence::validation_errors() const {
    std::vector<std::string> errors;

    if (condition_occurrence_id <= 0) {
        errors.push_back("condition_occurrence_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (condition_concept_id <= 0) {
        errors.push_back("condition_concept_id must be positive");
    }

    if (condition_type_concept_id <= 0) {
        errors.push_back("condition_type_concept_id must be positive");
    }

    return errors;
}

// DrugExposure implementation
std::string DrugExposure::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << drug_exposure_id << ", ";
    ss << person_id << ", ";
    ss << drug_concept_id << ", ";
    ss << format_datetime_sql(drug_exposure_start_date) << ", ";
    ss << (drug_exposure_start_datetime.has_value() ? format_datetime_sql(drug_exposure_start_datetime.value()) : "NULL") << ", ";
    ss << format_datetime_sql(drug_exposure_end_date) << ", ";
    ss << (drug_exposure_end_datetime.has_value() ? format_datetime_sql(drug_exposure_end_datetime.value()) : "NULL") << ", ";
    ss << (verbatim_end_date.has_value() ? format_datetime_sql(verbatim_end_date.value()) : "NULL") << ", ";
    ss << drug_type_concept_id << ", ";

    if (escape_values) {
        ss << (stop_reason.has_value() ? escape_string_sql(stop_reason.value()) : "NULL") << ", ";
    } else {
        ss << (stop_reason.has_value() ? "'" + stop_reason.value() + "'" : "NULL") << ", ";
    }

    ss << (refills.has_value() ? std::to_string(refills.value()) : "NULL") << ", ";
    ss << (quantity.has_value() ? std::to_string(quantity.value()) : "NULL") << ", ";
    ss << (days_supply.has_value() ? std::to_string(days_supply.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (sig.has_value() ? escape_string_sql(sig.value()) : "NULL") << ", ";
    } else {
        ss << (sig.has_value() ? "'" + sig.value() + "'" : "NULL") << ", ";
    }

    ss << (route_concept_id.has_value() ? std::to_string(route_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (lot_number.has_value() ? escape_string_sql(lot_number.value()) : "NULL") << ", ";
    } else {
        ss << (lot_number.has_value() ? "'" + lot_number.value() + "'" : "NULL") << ", ";
    }

    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (drug_source_value.has_value() ? escape_string_sql(drug_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (drug_source_value.has_value() ? "'" + drug_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (drug_source_concept_id.has_value() ? std::to_string(drug_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (route_source_value.has_value() ? escape_string_sql(route_source_value.value()) : "NULL") << ", ";
        ss << (dose_unit_source_value.has_value() ? escape_string_sql(dose_unit_source_value.value()) : "NULL");
    } else {
        ss << (route_source_value.has_value() ? "'" + route_source_value.value() + "'" : "NULL") << ", ";
        ss << (dose_unit_source_value.has_value() ? "'" + dose_unit_source_value.value() + "'" : "NULL");
    }

    ss << ")";
    return ss.str();
}

std::vector<std::string> DrugExposure::field_names() const {
    return {
        "drug_exposure_id", "person_id", "drug_concept_id", "drug_exposure_start_date",
        "drug_exposure_start_datetime", "drug_exposure_end_date", "drug_exposure_end_datetime",
        "verbatim_end_date", "drug_type_concept_id", "stop_reason", "refills",
        "quantity", "days_supply", "sig", "route_concept_id", "lot_number",
        "provider_id", "visit_occurrence_id", "visit_detail_id", "drug_source_value",
        "drug_source_concept_id", "route_source_value", "dose_unit_source_value"
    };
}

std::vector<std::any> DrugExposure::field_values() const {
    std::vector<std::any> values;
    values.reserve(23);

    values.push_back(drug_exposure_id);
    values.push_back(person_id);
    values.push_back(drug_concept_id);
    values.push_back(drug_exposure_start_date);
    values.push_back(drug_exposure_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(drug_exposure_end_date);
    values.push_back(drug_exposure_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(verbatim_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(drug_type_concept_id);
    values.push_back(stop_reason.value_or(""));
    values.push_back(refills.value_or(0));
    values.push_back(quantity.value_or(0.0));
    values.push_back(days_supply.value_or(0));
    values.push_back(sig.value_or(""));
    values.push_back(route_concept_id.value_or(0));
    values.push_back(lot_number.value_or(""));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(drug_source_value.value_or(""));
    values.push_back(drug_source_concept_id.value_or(0));
    values.push_back(route_source_value.value_or(""));
    values.push_back(dose_unit_source_value.value_or(""));

    return values;
}

void DrugExposure::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("drug_exposure_id", drug_exposure_id);
    visitor.visit("person_id", person_id);
    visitor.visit("drug_concept_id", drug_concept_id);
    visitor.visit("drug_exposure_start_date", drug_exposure_start_date);
    visitor.visit("drug_exposure_start_datetime", drug_exposure_start_datetime);
    visitor.visit("drug_exposure_end_date", drug_exposure_end_date);
    visitor.visit("drug_exposure_end_datetime", drug_exposure_end_datetime);
    visitor.visit("verbatim_end_date", verbatim_end_date);
    visitor.visit("drug_type_concept_id", drug_type_concept_id);
    visitor.visit("stop_reason", stop_reason);
    visitor.visit("refills", refills);
    visitor.visit("quantity", quantity);
    visitor.visit("days_supply", days_supply);
    visitor.visit("sig", sig);
    visitor.visit("route_concept_id", route_concept_id);
    visitor.visit("lot_number", lot_number);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("drug_source_value", drug_source_value);
    visitor.visit("drug_source_concept_id", drug_source_concept_id);
    visitor.visit("route_source_value", route_source_value);
    visitor.visit("dose_unit_source_value", dose_unit_source_value);
}

bool DrugExposure::validate() const {
    return drug_exposure_id > 0 &&
           person_id > 0 &&
           drug_concept_id > 0 &&
           drug_type_concept_id > 0 &&
           drug_exposure_start_date < drug_exposure_end_date &&
           validate_quantity() &&
           validate_days_supply();
}

std::vector<std::string> DrugExposure::validation_errors() const {
    std::vector<std::string> errors;

    if (drug_exposure_id <= 0) {
        errors.push_back("drug_exposure_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (drug_concept_id <= 0) {
        errors.push_back("drug_concept_id must be positive");
    }

    if (drug_type_concept_id <= 0) {
        errors.push_back("drug_type_concept_id must be positive");
    }

    if (drug_exposure_start_date >= drug_exposure_end_date) {
        errors.push_back("drug_exposure_start_date must be before drug_exposure_end_date");
    }

    if (!validate_quantity()) {
        errors.push_back("quantity must be positive");
    }

    if (!validate_days_supply()) {
        errors.push_back("days_supply must be positive");
    }

    return errors;
}

bool DrugExposure::validate_quantity() const {
    if (!quantity.has_value()) {
        return true; // Optional field
    }
    return quantity.value() > 0;
}

bool DrugExposure::validate_days_supply() const {
    if (!days_supply.has_value()) {
        return true; // Optional field
    }
    return days_supply.value() > 0;
}

// ProcedureOccurrence implementation
std::string ProcedureOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << procedure_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << procedure_concept_id << ", ";
    ss << format_datetime_sql(procedure_date) << ", ";
    ss << (procedure_datetime.has_value() ? format_datetime_sql(procedure_datetime.value()) : "NULL") << ", ";
    ss << (procedure_end_date.has_value() ? format_datetime_sql(procedure_end_date.value()) : "NULL") << ", ";
    ss << (procedure_end_datetime.has_value() ? format_datetime_sql(procedure_end_datetime.value()) : "NULL") << ", ";
    ss << procedure_type_concept_id << ", ";
    ss << (modifier_concept_id.has_value() ? std::to_string(modifier_concept_id.value()) : "NULL") << ", ";
    ss << (quantity.has_value() ? std::to_string(quantity.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (procedure_source_value.has_value() ? escape_string_sql(procedure_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (procedure_source_value.has_value() ? "'" + procedure_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (procedure_source_concept_id.has_value() ? std::to_string(procedure_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (modifier_source_value.has_value() ? escape_string_sql(modifier_source_value.value()) : "NULL");
    } else {
        ss << (modifier_source_value.has_value() ? "'" + modifier_source_value.value() + "'" : "NULL");
    }

    ss << ")";
    return ss.str();
}

std::vector<std::string> ProcedureOccurrence::field_names() const {
    return {
        "procedure_occurrence_id", "person_id", "procedure_concept_id", "procedure_date",
        "procedure_datetime", "procedure_end_date", "procedure_end_datetime",
        "procedure_type_concept_id", "modifier_concept_id", "quantity", "provider_id",
        "visit_occurrence_id", "visit_detail_id", "procedure_source_value",
        "procedure_source_concept_id", "modifier_source_value"
    };
}

std::vector<std::any> ProcedureOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);

    values.push_back(procedure_occurrence_id);
    values.push_back(person_id);
    values.push_back(procedure_concept_id);
    values.push_back(procedure_date);
    values.push_back(procedure_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_type_concept_id);
    values.push_back(modifier_concept_id.value_or(0));
    values.push_back(quantity.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(procedure_source_value.value_or(""));
    values.push_back(procedure_source_concept_id.value_or(0));
    values.push_back(modifier_source_value.value_or(""));

    return values;
}

void ProcedureOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("procedure_occurrence_id", procedure_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("procedure_concept_id", procedure_concept_id);
    visitor.visit("procedure_date", procedure_date);
    visitor.visit("procedure_datetime", procedure_datetime);
    visitor.visit("procedure_end_date", procedure_end_date);
    visitor.visit("procedure_end_datetime", procedure_end_datetime);
    visitor.visit("procedure_type_concept_id", procedure_type_concept_id);
    visitor.visit("modifier_concept_id", modifier_concept_id);
    visitor.visit("quantity", quantity);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("procedure_source_value", procedure_source_value);
    visitor.visit("procedure_source_concept_id", procedure_source_concept_id);
    visitor.visit("modifier_source_value", modifier_source_value);
}

bool ProcedureOccurrence::validate() const {
    return procedure_occurrence_id > 0 &&
           person_id > 0 &&
           procedure_concept_id > 0 &&
           procedure_type_concept_id > 0;
}

std::vector<std::string> ProcedureOccurrence::validation_errors() const {
    std::vector<std::string> errors;

    if (procedure_occurrence_id <= 0) {
        errors.push_back("procedure_occurrence_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (procedure_concept_id <= 0) {
        errors.push_back("procedure_concept_id must be positive");
    }

    if (procedure_type_concept_id <= 0) {
        errors.push_back("procedure_type_concept_id must be positive");
    }

    return errors;
}

// Measurement implementation
std::string Measurement::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << measurement_id << ", ";
    ss << person_id << ", ";
    ss << measurement_concept_id << ", ";
    ss << format_datetime_sql(measurement_date) << ", ";
    ss << (measurement_datetime.has_value() ? format_datetime_sql(measurement_datetime.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (measurement_time.has_value() ? escape_string_sql(measurement_time.value()) : "NULL") << ", ";
    } else {
        ss << (measurement_time.has_value() ? "'" + measurement_time.value() + "'" : "NULL") << ", ";
    }
    ss << measurement_type_concept_id << ", ";
    ss << (operator_concept_id.has_value() ? std::to_string(operator_concept_id.value()) : "NULL") << ", ";
    ss << (value_as_number.has_value() ? std::to_string(value_as_number.value()) : "NULL") << ", ";
    ss << (value_as_concept_id.has_value() ? std::to_string(value_as_concept_id.value()) : "NULL") << ", ";
    ss << (unit_concept_id.has_value() ? std::to_string(unit_concept_id.value()) : "NULL") << ", ";
    ss << (range_low.has_value() ? std::to_string(range_low.value()) : "NULL") << ", ";
    ss << (range_high.has_value() ? std::to_string(range_high.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (measurement_source_value.has_value() ? escape_string_sql(measurement_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (measurement_source_value.has_value() ? "'" + measurement_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (measurement_source_concept_id.has_value() ? std::to_string(measurement_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (unit_source_value.has_value() ? escape_string_sql(unit_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (unit_source_value.has_value() ? "'" + unit_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (unit_source_concept_id.has_value() ? std::to_string(unit_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (value_source_value.has_value() ? escape_string_sql(value_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (value_source_value.has_value() ? "'" + value_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (measurement_event_id.has_value() ? std::to_string(measurement_event_id.value()) : "NULL") << ", ";
    ss << (meas_event_field_concept_id.has_value() ? std::to_string(meas_event_field_concept_id.value()) : "NULL");

    ss << ")";
    return ss.str();
}

std::vector<std::string> Measurement::field_names() const {
    return {
        "measurement_id", "person_id", "measurement_concept_id", "measurement_date",
        "measurement_datetime", "measurement_time", "measurement_type_concept_id",
        "operator_concept_id", "value_as_number", "value_as_concept_id", "unit_concept_id",
        "range_low", "range_high", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "measurement_source_value", "measurement_source_concept_id", "unit_source_value",
        "unit_source_concept_id", "value_source_value", "measurement_event_id",
        "meas_event_field_concept_id"
    };
}

std::vector<std::any> Measurement::field_values() const {
    std::vector<std::any> values;
    values.reserve(23);

    values.push_back(measurement_id);
    values.push_back(person_id);
    values.push_back(measurement_concept_id);
    values.push_back(measurement_date);
    values.push_back(measurement_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(measurement_time.value_or(""));
    values.push_back(measurement_type_concept_id);
    values.push_back(operator_concept_id.value_or(0));
    values.push_back(value_as_number.value_or(0.0));
    values.push_back(value_as_concept_id.value_or(0));
    values.push_back(unit_concept_id.value_or(0));
    values.push_back(range_low.value_or(0.0));
    values.push_back(range_high.value_or(0.0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(measurement_source_value.value_or(""));
    values.push_back(measurement_source_concept_id.value_or(0));
    values.push_back(unit_source_value.value_or(""));
    values.push_back(unit_source_concept_id.value_or(0));
    values.push_back(value_source_value.value_or(""));
    values.push_back(measurement_event_id.value_or(0));
    values.push_back(meas_event_field_concept_id.value_or(0));

    return values;
}

void Measurement::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("measurement_id", measurement_id);
    visitor.visit("person_id", person_id);
    visitor.visit("measurement_concept_id", measurement_concept_id);
    visitor.visit("measurement_date", measurement_date);
    visitor.visit("measurement_datetime", measurement_datetime);
    visitor.visit("measurement_time", measurement_time);
    visitor.visit("measurement_type_concept_id", measurement_type_concept_id);
    visitor.visit("operator_concept_id", operator_concept_id);
    visitor.visit("value_as_number", value_as_number);
    visitor.visit("value_as_concept_id", value_as_concept_id);
    visitor.visit("unit_concept_id", unit_concept_id);
    visitor.visit("range_low", range_low);
    visitor.visit("range_high", range_high);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("measurement_source_value", measurement_source_value);
    visitor.visit("measurement_source_concept_id", measurement_source_concept_id);
    visitor.visit("unit_source_value", unit_source_value);
    visitor.visit("unit_source_concept_id", unit_source_concept_id);
    visitor.visit("value_source_value", value_source_value);
    visitor.visit("measurement_event_id", measurement_event_id);
    visitor.visit("meas_event_field_concept_id", meas_event_field_concept_id);
}

bool Measurement::validate() const {
    return measurement_id > 0 &&
           person_id > 0 &&
           measurement_concept_id > 0 &&
           measurement_type_concept_id > 0 &&
           validate_ranges();
}

std::vector<std::string> Measurement::validation_errors() const {
    std::vector<std::string> errors;

    if (measurement_id <= 0) {
        errors.push_back("measurement_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (measurement_concept_id <= 0) {
        errors.push_back("measurement_concept_id must be positive");
    }

    if (measurement_type_concept_id <= 0) {
        errors.push_back("measurement_type_concept_id must be positive");
    }

    if (!validate_ranges()) {
        errors.push_back("range_low must be less than or equal to range_high");
    }

    return errors;
}

bool Measurement::validate_ranges() const {
    if (!range_low.has_value() || !range_high.has_value()) {
        return true; // Both are optional
    }
    return range_low.value() <= range_high.value();
}

// Observation implementation
std::string Observation::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << observation_id << ", ";
    ss << person_id << ", ";
    ss << observation_concept_id << ", ";
    ss << format_datetime_sql(observation_date) << ", ";
    ss << (observation_datetime.has_value() ? format_datetime_sql(observation_datetime.value()) : "NULL") << ", ";
    ss << observation_type_concept_id << ", ";
    ss << (value_as_number.has_value() ? std::to_string(value_as_number.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (value_as_string.has_value() ? escape_string_sql(value_as_string.value()) : "NULL") << ", ";
    } else {
        ss << (value_as_string.has_value() ? "'" + value_as_string.value() + "'" : "NULL") << ", ";
    }

    ss << (value_as_concept_id.has_value() ? std::to_string(value_as_concept_id.value()) : "NULL") << ", ";
    ss << (qualifier_concept_id.has_value() ? std::to_string(qualifier_concept_id.value()) : "NULL") << ", ";
    ss << (unit_concept_id.has_value() ? std::to_string(unit_concept_id.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (observation_source_value.has_value() ? escape_string_sql(observation_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (observation_source_value.has_value() ? "'" + observation_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (observation_source_concept_id.has_value() ? std::to_string(observation_source_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (unit_source_value.has_value() ? escape_string_sql(unit_source_value.value()) : "NULL") << ", ";
        ss << (qualifier_source_value.has_value() ? escape_string_sql(qualifier_source_value.value()) : "NULL") << ", ";
        ss << (value_source_value.has_value() ? escape_string_sql(value_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (unit_source_value.has_value() ? "'" + unit_source_value.value() + "'" : "NULL") << ", ";
        ss << (qualifier_source_value.has_value() ? "'" + qualifier_source_value.value() + "'" : "NULL") << ", ";
        ss << (value_source_value.has_value() ? "'" + value_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (observation_event_id.has_value() ? std::to_string(observation_event_id.value()) : "NULL") << ", ";
    ss << (obs_event_field_concept_id.has_value() ? std::to_string(obs_event_field_concept_id.value()) : "NULL");

    ss << ")";
    return ss.str();
}

std::vector<std::string> Observation::field_names() const {
    return {
        "observation_id", "person_id", "observation_concept_id", "observation_date",
        "observation_datetime", "observation_type_concept_id", "value_as_number",
        "value_as_string", "value_as_concept_id", "qualifier_concept_id", "unit_concept_id",
        "provider_id", "visit_occurrence_id", "visit_detail_id", "observation_source_value",
        "observation_source_concept_id", "unit_source_value", "qualifier_source_value",
        "value_source_value", "observation_event_id", "obs_event_field_concept_id"
    };
}

std::vector<std::any> Observation::field_values() const {
    std::vector<std::any> values;
    values.reserve(21);

    values.push_back(observation_id);
    values.push_back(person_id);
    values.push_back(observation_concept_id);
    values.push_back(observation_date);
    values.push_back(observation_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(observation_type_concept_id);
    values.push_back(value_as_number.value_or(0.0));
    values.push_back(value_as_string.value_or(""));
    values.push_back(value_as_concept_id.value_or(0));
    values.push_back(qualifier_concept_id.value_or(0));
    values.push_back(unit_concept_id.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(observation_source_value.value_or(""));
    values.push_back(observation_source_concept_id.value_or(0));
    values.push_back(unit_source_value.value_or(""));
    values.push_back(qualifier_source_value.value_or(""));
    values.push_back(value_source_value.value_or(""));
    values.push_back(observation_event_id.value_or(0));
    values.push_back(obs_event_field_concept_id.value_or(0));

    return values;
}

void Observation::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("observation_id", observation_id);
    visitor.visit("person_id", person_id);
    visitor.visit("observation_concept_id", observation_concept_id);
    visitor.visit("observation_date", observation_date);
    visitor.visit("observation_datetime", observation_datetime);
    visitor.visit("observation_type_concept_id", observation_type_concept_id);
    visitor.visit("value_as_number", value_as_number);
    visitor.visit("value_as_string", value_as_string);
    visitor.visit("value_as_concept_id", value_as_concept_id);
    visitor.visit("qualifier_concept_id", qualifier_concept_id);
    visitor.visit("unit_concept_id", unit_concept_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("observation_source_value", observation_source_value);
    visitor.visit("observation_source_concept_id", observation_source_concept_id);
    visitor.visit("unit_source_value", unit_source_value);
    visitor.visit("qualifier_source_value", qualifier_source_value);
    visitor.visit("value_source_value", value_source_value);
    visitor.visit("observation_event_id", observation_event_id);
    visitor.visit("obs_event_field_concept_id", obs_event_field_concept_id);
}

bool Observation::validate() const {
    return observation_id > 0 &&
           person_id > 0 &&
           observation_concept_id > 0 &&
           observation_type_concept_id > 0;
}

std::vector<std::string> Observation::validation_errors() const {
    std::vector<std::string> errors;

    if (observation_id <= 0) {
        errors.push_back("observation_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (observation_concept_id <= 0) {
        errors.push_back("observation_concept_id must be positive");
    }

    if (observation_type_concept_id <= 0) {
        errors.push_back("observation_type_concept_id must be positive");
    }

    return errors;
}

// Death implementation
std::string Death::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << person_id << ", ";
    ss << format_datetime_sql(death_date) << ", ";
    ss << (death_datetime.has_value() ? format_datetime_sql(death_datetime.value()) : "NULL") << ", ";
    ss << (death_type_concept_id.has_value() ? std::to_string(death_type_concept_id.value()) : "NULL") << ", ";
    ss << (cause_concept_id.has_value() ? std::to_string(cause_concept_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (cause_source_value.has_value() ? escape_string_sql(cause_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (cause_source_value.has_value() ? "'" + cause_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (cause_source_concept_id.has_value() ? std::to_string(cause_source_concept_id.value()) : "NULL");

    ss << ")";
    return ss.str();
}

std::vector<std::string> Death::field_names() const {
    return {
        "person_id", "death_date", "death_datetime", "death_type_concept_id",
        "cause_concept_id", "cause_source_value", "cause_source_concept_id"
    };
}

std::vector<std::any> Death::field_values() const {
    std::vector<std::any> values;
    values.reserve(7);

    values.push_back(person_id);
    values.push_back(death_date);
    values.push_back(death_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(death_type_concept_id.value_or(0));
    values.push_back(cause_concept_id.value_or(0));
    values.push_back(cause_source_value.value_or(""));
    values.push_back(cause_source_concept_id.value_or(0));

    return values;
}

void Death::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("person_id", person_id);
    visitor.visit("death_date", death_date);
    visitor.visit("death_datetime", death_datetime);
    visitor.visit("death_type_concept_id", death_type_concept_id);
    visitor.visit("cause_concept_id", cause_concept_id);
    visitor.visit("cause_source_value", cause_source_value);
    visitor.visit("cause_source_concept_id", cause_source_concept_id);
}

bool Death::validate() const {
    return person_id > 0 && validate_death_date();
}

std::vector<std::string> Death::validation_errors() const {
    std::vector<std::string> errors;

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (!validate_death_date()) {
        errors.push_back("death_date cannot be in the future");
    }

    return errors;
}

bool Death::validate_death_date() const {
    auto now = std::chrono::system_clock::now();
    return death_date <= now;
}

// Note implementation
std::string Note::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";

    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }

    ss << ") VALUES (";
    ss << note_id << ", ";
    ss << person_id << ", ";
    ss << format_datetime_sql(note_date) << ", ";
    ss << (note_datetime.has_value() ? format_datetime_sql(note_datetime.value()) : "NULL") << ", ";
    ss << note_type_concept_id << ", ";
    ss << note_class_concept_id << ", ";

    if (escape_values) {
        ss << escape_string_sql(note_title) << ", ";
        ss << escape_string_sql(note_text) << ", ";
    } else {
        ss << "'" << note_title << "', ";
        ss << "'" << note_text << "', ";
    }

    ss << encoding_concept_id << ", ";
    ss << language_concept_id << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";

    if (escape_values) {
        ss << (note_source_value.has_value() ? escape_string_sql(note_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (note_source_value.has_value() ? "'" + note_source_value.value() + "'" : "NULL") << ", ";
    }

    ss << (note_event_id.has_value() ? std::to_string(note_event_id.value()) : "NULL") << ", ";
    ss << (note_event_field_concept_id.has_value() ? std::to_string(note_event_field_concept_id.value()) : "NULL");

    ss << ")";
    return ss.str();
}

std::vector<std::string> Note::field_names() const {
    return {
        "note_id", "person_id", "note_date", "note_datetime", "note_type_concept_id",
        "note_class_concept_id", "note_title", "note_text", "encoding_concept_id",
        "language_concept_id", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "note_source_value", "note_event_id", "note_event_field_concept_id"
    };
}

std::vector<std::any> Note::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);

    values.push_back(note_id);
    values.push_back(person_id);
    values.push_back(note_date);
    values.push_back(note_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(note_type_concept_id);
    values.push_back(note_class_concept_id);
    values.push_back(note_title);
    values.push_back(note_text);
    values.push_back(encoding_concept_id);
    values.push_back(language_concept_id);
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(note_source_value.value_or(""));
    values.push_back(note_event_id.value_or(0));
    values.push_back(note_event_field_concept_id.value_or(0));

    return values;
}

void Note::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("note_id", note_id);
    visitor.visit("person_id", person_id);
    visitor.visit("note_date", note_date);
    visitor.visit("note_datetime", note_datetime);
    visitor.visit("note_type_concept_id", note_type_concept_id);
    visitor.visit("note_class_concept_id", note_class_concept_id);
    visitor.visit("note_title", note_title);
    visitor.visit("note_text", note_text);
    visitor.visit("encoding_concept_id", encoding_concept_id);
    visitor.visit("language_concept_id", language_concept_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("note_source_value", note_source_value);
    visitor.visit("note_event_id", note_event_id);
    visitor.visit("note_event_field_concept_id", note_event_field_concept_id);
}

bool Note::validate() const {
    return note_id > 0 &&
           person_id > 0 &&
           note_type_concept_id > 0 &&
           note_class_concept_id > 0;
}

std::vector<std::string> Note::validation_errors() const {
    std::vector<std::string> errors;

    if (note_id <= 0) {
        errors.push_back("note_id must be positive");
    }

    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }

    if (note_type_concept_id <= 0) {
        errors.push_back("note_type_concept_id must be positive");
    }

    if (note_class_concept_id <= 0) {
        errors.push_back("note_class_concept_id must be positive");
    }

    return errors;
}

// Concept implementation
std::string Concept::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss.imbue(std::locale::classic()); // Use C locale for numeric formatting (no thousand separators)
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    ss << ") VALUES (";
    ss << concept_id << ", ";
    if (escape_values) {
        ss << escape_string_sql(concept_name) << ", ";
        ss << escape_string_sql(domain_id) << ", ";
        ss << escape_string_sql(vocabulary_id) << ", ";
        ss << escape_string_sql(concept_class_id) << ", ";
        ss << escape_string_sql(concept_code) << ", ";
    } else {
        ss << "'" << concept_name << "', ";
        ss << "'" << domain_id << "', ";
        ss << "'" << vocabulary_id << "', ";
        ss << "'" << concept_class_id << "', ";
        ss << "'" << concept_code << "', ";
    }
    ss << format_datetime_sql(valid_start_date) << ", ";
    ss << format_datetime_sql(valid_end_date) << ", ";
    if (escape_values) {
        ss << (standard_concept.has_value() ? escape_string_sql(standard_concept.value()) : "NULL") << ", ";
        ss << (invalid_reason.has_value() ? escape_string_sql(invalid_reason.value()) : "NULL");
    } else {
        ss << (standard_concept.has_value() ? "'" + standard_concept.value() + "'" : "NULL") << ", ";
        ss << (invalid_reason.has_value() ? "'" + invalid_reason.value() + "'" : "NULL");
    }
    ss << ")";
    return ss.str();
}

std::vector<std::string> Concept::field_names() const {
    return {
        "concept_id", "concept_name", "domain_id", "vocabulary_id",
        "concept_class_id", "concept_code", "valid_start_date",
        "valid_end_date", "standard_concept", "invalid_reason"
    };
}

std::vector<std::any> Concept::field_values() const {
    std::vector<std::any> values;
    values.reserve(10);
    values.push_back(concept_id);
    values.push_back(concept_name);
    values.push_back(domain_id);
    values.push_back(vocabulary_id);
    values.push_back(concept_class_id);
    values.push_back(concept_code);
    values.push_back(valid_start_date);
    values.push_back(valid_end_date);
    values.push_back(standard_concept.value_or(""));
    values.push_back(invalid_reason.value_or(""));
    return values;
}

void Concept::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("concept_id", concept_id);
    visitor.visit("concept_name", concept_name);
    visitor.visit("domain_id", domain_id);
    visitor.visit("vocabulary_id", vocabulary_id);
    visitor.visit("concept_class_id", concept_class_id);
    visitor.visit("concept_code", concept_code);
    visitor.visit("valid_start_date", valid_start_date);
    visitor.visit("valid_end_date", valid_end_date);
    visitor.visit("standard_concept", standard_concept);
    visitor.visit("invalid_reason", invalid_reason);
}

bool Concept::validate() const {
    return concept_id > 0 &&
           !concept_name.empty() &&
           !domain_id.empty() &&
           !vocabulary_id.empty() &&
           !concept_class_id.empty() &&
           !concept_code.empty() &&
           valid_start_date <= valid_end_date;
}

std::vector<std::string> Concept::validation_errors() const {
    std::vector<std::string> errors;
    if (concept_id <= 0) {
        errors.push_back("concept_id must be positive");
    }
    if (concept_name.empty()) {
        errors.push_back("concept_name cannot be empty");
    }
    if (domain_id.empty()) {
        errors.push_back("domain_id cannot be empty");
    }
    if (vocabulary_id.empty()) {
        errors.push_back("vocabulary_id cannot be empty");
    }
    if (concept_class_id.empty()) {
        errors.push_back("concept_class_id cannot be empty");
    }
    if (concept_code.empty()) {
        errors.push_back("concept_code cannot be empty");
    }
    if (valid_start_date > valid_end_date) {
        errors.push_back("valid_start_date must be before or equal to valid_end_date");
    }
    return errors;
}

// Location implementation
std::string Location::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    ss << ") VALUES (";
    ss << location_id << ", ";
    if (escape_values) {
        ss << (address_1.has_value() ? escape_string_sql(address_1.value()) : "NULL") << ", ";
        ss << (address_2.has_value() ? escape_string_sql(address_2.value()) : "NULL") << ", ";
        ss << (city.has_value() ? escape_string_sql(city.value()) : "NULL") << ", ";
        ss << (state.has_value() ? escape_string_sql(state.value()) : "NULL") << ", ";
        ss << (zip.has_value() ? escape_string_sql(zip.value()) : "NULL") << ", ";
        ss << (county.has_value() ? escape_string_sql(county.value()) : "NULL") << ", ";
        ss << (country.has_value() ? escape_string_sql(country.value()) : "NULL") << ", ";
        ss << (location_source_value.has_value() ? escape_string_sql(location_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (address_1.has_value() ? "'" + address_1.value() + "'" : "NULL") << ", ";
        ss << (address_2.has_value() ? "'" + address_2.value() + "'" : "NULL") << ", ";
        ss << (city.has_value() ? "'" + city.value() + "'" : "NULL") << ", ";
        ss << (state.has_value() ? "'" + state.value() + "'" : "NULL") << ", ";
        ss << (zip.has_value() ? "'" + zip.value() + "'" : "NULL") << ", ";
        ss << (county.has_value() ? "'" + county.value() + "'" : "NULL") << ", ";
        ss << (country.has_value() ? "'" + country.value() + "'" : "NULL") << ", ";
        ss << (location_source_value.has_value() ? "'" + location_source_value.value() + "'" : "NULL") << ", ";
    }
    ss << (latitude.has_value() ? std::to_string(latitude.value()) : "NULL") << ", ";
    ss << (longitude.has_value() ? std::to_string(longitude.value()) : "NULL");
    ss << ")";
    return ss.str();
}

std::vector<std::string> Location::field_names() const {
    return {
        "location_id", "address_1", "address_2", "city", "state",
        "zip", "county", "country", "location_source_value",
        "latitude", "longitude"
    };
}

std::vector<std::any> Location::field_values() const {
    std::vector<std::any> values;
    values.reserve(11);
    values.push_back(location_id);
    values.push_back(address_1.value_or(""));
    values.push_back(address_2.value_or(""));
    values.push_back(city.value_or(""));
    values.push_back(state.value_or(""));
    values.push_back(zip.value_or(""));
    values.push_back(county.value_or(""));
    values.push_back(country.value_or(""));
    values.push_back(location_source_value.value_or(""));
    values.push_back(latitude.value_or(0.0));
    values.push_back(longitude.value_or(0.0));
    return values;
}

void Location::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("location_id", location_id);
    visitor.visit("address_1", address_1);
    visitor.visit("address_2", address_2);
    visitor.visit("city", city);
    visitor.visit("state", state);
    visitor.visit("zip", zip);
    visitor.visit("county", county);
    visitor.visit("country", country);
    visitor.visit("location_source_value", location_source_value);
    visitor.visit("latitude", latitude);
    visitor.visit("longitude", longitude);
}

bool Location::validate() const {
    return location_id > 0 && validate_uk_postcode();
}

std::vector<std::string> Location::validation_errors() const {
    std::vector<std::string> errors;
    if (location_id <= 0) {
        errors.push_back("location_id must be positive");
    }
    if (zip.has_value() && !validate_uk_postcode()) {
        errors.push_back("Invalid UK postcode format");
    }
    return errors;
}

bool Location::validate_uk_postcode() const {
    if (!zip.has_value() || zip.value().empty()) {
        return true; // Optional field
    }
    
    std::string postcode = zip.value();
    
    // Check for whitespace-only strings
    if (std::all_of(postcode.begin(), postcode.end(), ::isspace)) {
        return false; // Whitespace-only is invalid
    }
    
    // Clean and normalize: preserve single internal space, remove extra spaces, convert to uppercase
    std::string cleaned_postcode;
    bool in_space = false;
    bool has_space = false;
    
    for (char c : postcode) {
        if (std::isspace(c)) {
            if (!in_space && !cleaned_postcode.empty()) {
                cleaned_postcode += ' ';
                has_space = true;
                in_space = true;
            }
        } else {
            cleaned_postcode += std::toupper(c);
            in_space = false;
        }
    }
    
    // UK postcodes must have exactly one space between district and sector
    // If no space, try to add one automatically for common patterns
    if (!has_space) {
        // Try to add space for common patterns like SW1A1AA -> SW1A 1AA
        if (cleaned_postcode.length() >= 5 && cleaned_postcode.length() <= 7) {
            std::string first_part = cleaned_postcode.substr(0, cleaned_postcode.length() - 3);
            std::string last_part = cleaned_postcode.substr(cleaned_postcode.length() - 3);
            cleaned_postcode = first_part + " " + last_part;
            has_space = true;
        } else {
            return false; // No space and can't format - invalid
        }
    }
    
    // Comprehensive UK postcode validation patterns
    // Covers all valid UK postcode formats:
    // A9 9AA, A99 9AA, AA9 9AA, AA99 9AA, A9A 9AA, AA9A 9AA
    std::vector<std::regex> patterns = {
        std::regex(R"(^[A-Z][0-9] [0-9][A-Z]{2}$)"),      // A9 9AA
        std::regex(R"(^[A-Z][0-9]{2} [0-9][A-Z]{2}$)"),    // A99 9AA  
        std::regex(R"(^[A-Z]{2}[0-9] [0-9][A-Z]{2}$)"),    // AA9 9AA
        std::regex(R"(^[A-Z]{2}[0-9]{2} [0-9][A-Z]{2}$)"), // AA99 9AA
        std::regex(R"(^[A-Z][0-9][A-Z] [0-9][A-Z]{2}$)"),  // A9A 9AA
        std::regex(R"(^[A-Z]{2}[0-9][A-Z] [0-9][A-Z]{2}$)") // AA9A 9AA
    };
    
    // Check against all valid patterns
    for (const auto& pattern : patterns) {
        if (std::regex_match(cleaned_postcode, pattern)) {
            // Additional validation for specific UK postcode rules
            
            // First character restrictions
            char first_char = cleaned_postcode[0];
            if (first_char == 'Q' || first_char == 'V' || first_char == 'X') {
                return false; // These letters are not used in UK postcodes
            }
            
            // Second character restrictions (if it's a letter)
            if (cleaned_postcode.length() >= 2 && std::isalpha(cleaned_postcode[1])) {
                char second_char = cleaned_postcode[1];
                if (second_char == 'I' || second_char == 'J' || second_char == 'Z') {
                    return false; // These letters are not used as second character
                }
            }
            
            // Third character restrictions for A9A pattern (very limited restrictions)
            size_t space_pos = cleaned_postcode.find(' ');
            if (space_pos == 3 && std::isalpha(cleaned_postcode[2])) {
                char third_char = cleaned_postcode[2];
                // Only a few letters are actually restricted in the third position
                if (third_char == 'I' || third_char == 'O') {
                    return false; // These letters are not used as third character
                }
            }
            
            // Final character restrictions (last two characters)
            if (space_pos != std::string::npos && space_pos + 3 < cleaned_postcode.length()) {
                char penultimate_char = cleaned_postcode[space_pos + 2];
                char last_char = cleaned_postcode[space_pos + 3];
                
                if (penultimate_char == 'C' || penultimate_char == 'I' || 
                    penultimate_char == 'K' || penultimate_char == 'M' ||
                    penultimate_char == 'O' || penultimate_char == 'V') {
                    return false; // These letters are not used in final part
                }
                
                if (last_char == 'C' || last_char == 'I' || 
                    last_char == 'K' || last_char == 'M' ||
                    last_char == 'O' || last_char == 'V') {
                    return false; // These letters are not used in final part
                }
            }
            
            return true; // Valid UK postcode
        }
    }
    
    return false; // No pattern matched
}

// CareSite implementation
std::string CareSite::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss.imbue(std::locale::classic()); // Use C locale for numeric formatting
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    ss << ") VALUES (";
    ss << care_site_id << ", ";
    if (escape_values) {
        ss << (care_site_name.has_value() ? escape_string_sql(care_site_name.value()) : "NULL") << ", ";
    } else {
        ss << (care_site_name.has_value() ? "'" + care_site_name.value() + "'" : "NULL") << ", ";
    }
    ss << (place_of_service_concept_id.has_value() ? std::to_string(place_of_service_concept_id.value()) : "NULL") << ", ";
    ss << (location_id.has_value() ? std::to_string(location_id.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (care_site_source_value.has_value() ? escape_string_sql(care_site_source_value.value()) : "NULL") << ", ";
        ss << (place_of_service_source_value.has_value() ? escape_string_sql(place_of_service_source_value.value()) : "NULL");
    } else {
        ss << (care_site_source_value.has_value() ? "'" + care_site_source_value.value() + "'" : "NULL") << ", ";
        ss << (place_of_service_source_value.has_value() ? "'" + place_of_service_source_value.value() + "'" : "NULL");
    }
    ss << ")";
    return ss.str();
}

std::vector<std::string> CareSite::field_names() const {
    return {
        "care_site_id", "care_site_name", "place_of_service_concept_id",
        "location_id", "care_site_source_value", "place_of_service_source_value"
    };
}

std::vector<std::any> CareSite::field_values() const {
    std::vector<std::any> values;
    values.reserve(6);
    values.push_back(care_site_id);
    values.push_back(care_site_name.value_or(""));
    values.push_back(place_of_service_concept_id.value_or(0));
    values.push_back(location_id.value_or(0));
    values.push_back(care_site_source_value.value_or(""));
    values.push_back(place_of_service_source_value.value_or(""));
    return values;
}

void CareSite::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("care_site_name", care_site_name);
    visitor.visit("place_of_service_concept_id", place_of_service_concept_id);
    visitor.visit("location_id", location_id);
    visitor.visit("care_site_source_value", care_site_source_value);
    visitor.visit("place_of_service_source_value", place_of_service_source_value);
}

bool CareSite::validate() const {
    return care_site_id > 0;
}

std::vector<std::string> CareSite::validation_errors() const {
    std::vector<std::string> errors;
    if (care_site_id <= 0) {
        errors.push_back("care_site_id must be positive");
    }
    return errors;
}

// Provider implementation
std::string Provider::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss.imbue(std::locale::classic()); // Use C locale for numeric formatting
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    ss << ") VALUES (";
    ss << provider_id << ", ";
    if (escape_values) {
        ss << (provider_name.has_value() ? escape_string_sql(provider_name.value()) : "NULL") << ", ";
        ss << (npi.has_value() ? escape_string_sql(npi.value()) : "NULL") << ", ";
        ss << (dea.has_value() ? escape_string_sql(dea.value()) : "NULL") << ", ";
    } else {
        ss << (provider_name.has_value() ? "'" + provider_name.value() + "'" : "NULL") << ", ";
        ss << (npi.has_value() ? "'" + npi.value() + "'" : "NULL") << ", ";
        ss << (dea.has_value() ? "'" + dea.value() + "'" : "NULL") << ", ";
    }
    ss << (specialty_concept_id.has_value() ? std::to_string(specialty_concept_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";
    ss << (year_of_birth.has_value() ? std::to_string(year_of_birth.value()) : "NULL") << ", ";
    ss << (gender_concept_id.has_value() ? std::to_string(gender_concept_id.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (provider_source_value.has_value() ? escape_string_sql(provider_source_value.value()) : "NULL") << ", ";
        ss << (specialty_source_value.has_value() ? escape_string_sql(specialty_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (provider_source_value.has_value() ? "'" + provider_source_value.value() + "'" : "NULL") << ", ";
        ss << (specialty_source_value.has_value() ? "'" + specialty_source_value.value() + "'" : "NULL") << ", ";
    }
    ss << (specialty_source_concept_id.has_value() ? std::to_string(specialty_source_concept_id.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (gender_source_value.has_value() ? escape_string_sql(gender_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (gender_source_value.has_value() ? "'" + gender_source_value.value() + "'" : "NULL") << ", ";
    }
    ss << (gender_source_concept_id.has_value() ? std::to_string(gender_source_concept_id.value()) : "NULL");
    ss << ")";
    return ss.str();
}

std::vector<std::string> Provider::field_names() const {
    return {
        "provider_id", "provider_name", "npi", "dea", "specialty_concept_id",
        "care_site_id", "year_of_birth", "gender_concept_id", "provider_source_value",
        "specialty_source_value", "specialty_source_concept_id", "gender_source_value",
        "gender_source_concept_id"
    };
}

std::vector<std::any> Provider::field_values() const {
    std::vector<std::any> values;
    values.reserve(13);
    values.push_back(provider_id);
    values.push_back(provider_name.value_or(""));
    values.push_back(npi.value_or(""));
    values.push_back(dea.value_or(""));
    values.push_back(specialty_concept_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(year_of_birth.value_or(0));
    values.push_back(gender_concept_id.value_or(0));
    values.push_back(provider_source_value.value_or(""));
    values.push_back(specialty_source_value.value_or(""));
    values.push_back(specialty_source_concept_id.value_or(0));
    values.push_back(gender_source_value.value_or(""));
    values.push_back(gender_source_concept_id.value_or(0));
    return values;
}

void Provider::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("provider_id", provider_id);
    visitor.visit("provider_name", provider_name);
    visitor.visit("npi", npi);
    visitor.visit("dea", dea);
    visitor.visit("specialty_concept_id", specialty_concept_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("year_of_birth", year_of_birth);
    visitor.visit("gender_concept_id", gender_concept_id);
    visitor.visit("provider_source_value", provider_source_value);
    visitor.visit("specialty_source_value", specialty_source_value);
    visitor.visit("specialty_source_concept_id", specialty_source_concept_id);
    visitor.visit("gender_source_value", gender_source_value);
    visitor.visit("gender_source_concept_id", gender_source_concept_id);
}

bool Provider::validate() const {
    return provider_id > 0;
}

std::vector<std::string> Provider::validation_errors() const {
    std::vector<std::string> errors;
    if (provider_id <= 0) {
        errors.push_back("provider_id must be positive");
    }
    return errors;
}

// VisitDetail implementation
std::string VisitDetail::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss.imbue(std::locale::classic()); // Use C locale for numeric formatting
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    ss << ") VALUES (";
    ss << visit_detail_id << ", ";
    ss << person_id << ", ";
    ss << visit_detail_concept_id << ", ";
    ss << format_datetime_sql(visit_detail_start_date) << ", ";
    ss << format_datetime_sql(visit_detail_end_date) << ", ";
    ss << visit_detail_type_concept_id << ", ";
    ss << visit_occurrence_id << ", ";
    ss << (visit_detail_start_datetime.has_value() ? format_datetime_sql(visit_detail_start_datetime.value()) : "NULL") << ", ";
    ss << (visit_detail_end_datetime.has_value() ? format_datetime_sql(visit_detail_end_datetime.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (visit_detail_source_value.has_value() ? escape_string_sql(visit_detail_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (visit_detail_source_value.has_value() ? "'" + visit_detail_source_value.value() + "'" : "NULL") << ", ";
    }
    ss << (visit_detail_source_concept_id.has_value() ? std::to_string(visit_detail_source_concept_id.value()) : "NULL") << ", ";
    ss << (admitted_from_concept_id.has_value() ? std::to_string(admitted_from_concept_id.value()) : "NULL") << ", ";
    if (escape_values) {
        ss << (admitted_from_source_value.has_value() ? escape_string_sql(admitted_from_source_value.value()) : "NULL") << ", ";
        ss << (discharged_to_source_value.has_value() ? escape_string_sql(discharged_to_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (admitted_from_source_value.has_value() ? "'" + admitted_from_source_value.value() + "'" : "NULL") << ", ";
        ss << (discharged_to_source_value.has_value() ? "'" + discharged_to_source_value.value() + "'" : "NULL") << ", ";
    }
    ss << (discharged_to_concept_id.has_value() ? std::to_string(discharged_to_concept_id.value()) : "NULL") << ", ";
    ss << (preceding_visit_detail_id.has_value() ? std::to_string(preceding_visit_detail_id.value()) : "NULL") << ", ";
    ss << (parent_visit_detail_id.has_value() ? std::to_string(parent_visit_detail_id.value()) : "NULL");
    ss << ")";
    return ss.str();
}

std::vector<std::string> VisitDetail::field_names() const {
    return {
        "visit_detail_id", "person_id", "visit_detail_concept_id",
        "visit_detail_start_date", "visit_detail_end_date", "visit_detail_type_concept_id",
        "visit_occurrence_id", "visit_detail_start_datetime", "visit_detail_end_datetime",
        "provider_id", "care_site_id", "visit_detail_source_value",
        "visit_detail_source_concept_id", "admitted_from_concept_id",
        "admitted_from_source_value", "discharged_to_concept_id",
        "discharged_to_source_value", "preceding_visit_detail_id", "parent_visit_detail_id"
    };
}

std::vector<std::any> VisitDetail::field_values() const {
    std::vector<std::any> values;
    values.reserve(19);
    values.push_back(visit_detail_id);
    values.push_back(person_id);
    values.push_back(visit_detail_concept_id);
    values.push_back(visit_detail_start_date);
    values.push_back(visit_detail_end_date);
    values.push_back(visit_detail_type_concept_id);
    values.push_back(visit_occurrence_id);
    values.push_back(visit_detail_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(visit_detail_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(provider_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(visit_detail_source_value.value_or(""));
    values.push_back(visit_detail_source_concept_id.value_or(0));
    values.push_back(admitted_from_concept_id.value_or(0));
    values.push_back(admitted_from_source_value.value_or(""));
    values.push_back(discharged_to_concept_id.value_or(0));
    values.push_back(discharged_to_source_value.value_or(""));
    values.push_back(preceding_visit_detail_id.value_or(0));
    values.push_back(parent_visit_detail_id.value_or(0));
    return values;
}

void VisitDetail::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("person_id", person_id);
    visitor.visit("visit_detail_concept_id", visit_detail_concept_id);
    visitor.visit("visit_detail_start_date", visit_detail_start_date);
    visitor.visit("visit_detail_end_date", visit_detail_end_date);
    visitor.visit("visit_detail_type_concept_id", visit_detail_type_concept_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_start_datetime", visit_detail_start_datetime);
    visitor.visit("visit_detail_end_datetime", visit_detail_end_datetime);
    visitor.visit("provider_id", provider_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("visit_detail_source_value", visit_detail_source_value);
    visitor.visit("visit_detail_source_concept_id", visit_detail_source_concept_id);
    visitor.visit("admitted_from_concept_id", admitted_from_concept_id);
    visitor.visit("admitted_from_source_value", admitted_from_source_value);
    visitor.visit("discharged_to_concept_id", discharged_to_concept_id);
    visitor.visit("discharged_to_source_value", discharged_to_source_value);
    visitor.visit("preceding_visit_detail_id", preceding_visit_detail_id);
    visitor.visit("parent_visit_detail_id", parent_visit_detail_id);
}

bool VisitDetail::validate() const {
    return visit_detail_id > 0 &&
           person_id > 0 &&
           visit_detail_concept_id > 0 &&
           visit_detail_type_concept_id > 0 &&
           visit_occurrence_id > 0 &&
           visit_detail_start_date <= visit_detail_end_date;
}

std::vector<std::string> VisitDetail::validation_errors() const {
    std::vector<std::string> errors;
    if (visit_detail_id <= 0) {
        errors.push_back("visit_detail_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (visit_detail_concept_id <= 0) {
        errors.push_back("visit_detail_concept_id must be positive");
    }
    if (visit_detail_type_concept_id <= 0) {
        errors.push_back("visit_detail_type_concept_id must be positive");
    }
    if (visit_occurrence_id <= 0) {
        errors.push_back("visit_occurrence_id must be positive");
    }
    if (visit_detail_start_date > visit_detail_end_date) {
        errors.push_back("visit_detail_start_date must be before or equal to visit_detail_end_date");
    }
    return errors;
}

// OmopTableFactory implementation
// Static mutex for thread-safe factory operations
static std::mutex& get_factory_mutex() {
    static std::mutex factory_mutex;
    return factory_mutex;
}

std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>&
OmopTableFactory::get_creators() {
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>> creators = {
        {"person", []() { return std::make_unique<Person>(); }},
        {"observation_period", []() { return std::make_unique<ObservationPeriod>(); }},
        {"visit_occurrence", []() { return std::make_unique<VisitOccurrence>(); }},
        {"condition_occurrence", []() { return std::make_unique<ConditionOccurrence>(); }},
        {"drug_exposure", []() { return std::make_unique<DrugExposure>(); }},
        {"procedure_occurrence", []() { return std::make_unique<ProcedureOccurrence>(); }},
        {"measurement", []() { return std::make_unique<Measurement>(); }},
        {"observation", []() { return std::make_unique<Observation>(); }},
        {"death", []() { return std::make_unique<Death>(); }},
        {"note", []() { return std::make_unique<Note>(); }},
        {"concept", []() { return std::make_unique<Concept>(); }},
        {"location", []() { return std::make_unique<Location>(); }},
        {"care_site", []() { return std::make_unique<CareSite>(); }},
        {"provider", []() { return std::make_unique<Provider>(); }},
        {"visit_detail", []() { return std::make_unique<VisitDetail>(); }}
    };
    return creators;
}

std::unique_ptr<OmopTable> OmopTableFactory::create(const std::string& table_name) {
    if (table_name.empty()) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(get_factory_mutex());
    auto& creators = get_creators();
    auto it = creators.find(table_name);
    if (it != creators.end()) {
        try {
            return it->second();
        } catch (const std::exception&) {
            return nullptr;
        }
    }
    return nullptr;
}

std::vector<std::string> OmopTableFactory::get_supported_tables() {
    std::lock_guard<std::mutex> lock(get_factory_mutex());
    auto& creators = get_creators();
    std::vector<std::string> tables;
    tables.reserve(creators.size());

    for (const auto& [name, creator] : creators) {
        tables.push_back(name);
    }

    return tables;
}

bool OmopTableFactory::is_supported(const std::string& table_name) {
    std::lock_guard<std::mutex> lock(get_factory_mutex());
    auto& creators = get_creators();
    return creators.find(table_name) != creators.end();
}

void OmopTableFactory::register_table(
    const std::string& table_name,
    std::function<std::unique_ptr<OmopTable>()> creator) {

    if (table_name.empty() || !creator) {
        throw std::invalid_argument("Table name cannot be empty and creator cannot be null");
    }

    std::lock_guard<std::mutex> lock(get_factory_mutex());
    auto& creators = get_creators();
    creators[table_name] = creator;

}
void OmopTableFactory::unregister_table(const std::string& table_name) {
    if (table_name.empty()) {
        return;
    }

    std::lock_guard<std::mutex> lock(get_factory_mutex());
    auto& creators = get_creators();
    creators.erase(table_name);
}

// OmopSchema implementation
std::string OmopSchema::get_create_table_sql(
    const std::string& table_name,
    const std::string& schema_name) {

    if (table_name.empty()) {
        throw std::invalid_argument("Table name cannot be empty");
    }

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_create_table_sql(schema_name);
}

std::vector<std::string> OmopSchema::get_all_create_table_sql(
    const std::string& schema_name) {

    auto& schema_def = SchemaDefinitions::instance();
    auto table_order = schema_def.get_creation_order();
    std::vector<std::string> sql_statements;
    sql_statements.reserve(table_order.size());

    for (const auto& table_name : table_order) {
        try {
            sql_statements.push_back(get_create_table_sql(table_name, schema_name));
        } catch (const std::exception& e) {
            // Log error but continue with other tables
            // In production, this should use proper logging
            continue;
        }
    }

    return sql_statements;
}

std::vector<std::string> OmopSchema::get_table_indexes(
    const std::string& table_name,
    const std::string& schema_name) {

    if (table_name.empty()) {
        throw std::invalid_argument("Table name cannot be empty");
    }

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_create_index_sql(schema_name);
}

std::vector<std::string> OmopSchema::get_foreign_keys(
    const std::string& table_name,
    const std::string& schema_name) {

    if (table_name.empty()) {
        throw std::invalid_argument("Table name cannot be empty");
    }

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_foreign_key_sql(schema_name);
}

// Person validation_errors implementation
std::vector<std::string> Person::validation_errors() const {
    std::vector<std::string> errors;
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (gender_concept_id <= 0) {
        errors.push_back("gender_concept_id must be positive");
    }
    if (!validate_year_of_birth()) {
        errors.push_back("year_of_birth must be between 1900 and current year");
    }
    if (month_of_birth.has_value() && !validate_month_of_birth()) {
        errors.push_back("month_of_birth must be between 1 and 12");
    }
    if (day_of_birth.has_value() && !validate_day_of_birth()) {
        errors.push_back("day_of_birth must be valid for the given month and year");
    }
    if (race_concept_id <= 0) {
        errors.push_back("race_concept_id must be positive");
    }
    if (ethnicity_concept_id <= 0) {
        errors.push_back("ethnicity_concept_id must be positive");
    }
    
    return errors;
}

// ObservationPeriod validation_errors implementation
std::vector<std::string> ObservationPeriod::validation_errors() const {
    std::vector<std::string> errors;
    
    if (observation_period_id <= 0) {
        errors.push_back("observation_period_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (period_type_concept_id <= 0) {
        errors.push_back("period_type_concept_id must be positive");
    }
    if (!validate_date_range()) {
        errors.push_back("observation_period_start_date must be before or equal to observation_period_end_date");
    }
    
    return errors;
}

// VisitOccurrence validation_errors implementation
std::vector<std::string> VisitOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (visit_occurrence_id <= 0) {
        errors.push_back("visit_occurrence_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (visit_concept_id <= 0) {
        errors.push_back("visit_concept_id must be positive");
    }
    if (visit_type_concept_id <= 0) {
        errors.push_back("visit_type_concept_id must be positive");
    }
    if (visit_start_date > visit_end_date) {
        errors.push_back("visit_start_date must be before or equal to visit_end_date");
    }
    
    return errors;
}

// ConditionOccurrence validation_errors implementation
std::vector<std::string> ConditionOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (condition_occurrence_id <= 0) {
        errors.push_back("condition_occurrence_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (condition_concept_id <= 0) {
        errors.push_back("condition_concept_id must be positive");
    }
    if (condition_type_concept_id <= 0) {
        errors.push_back("condition_type_concept_id must be positive");
    }
    if (condition_end_date.has_value() && condition_start_date > condition_end_date.value()) {
        errors.push_back("condition_start_date must be before or equal to condition_end_date");
    }
    
    return errors;
}

// DrugExposure validation_errors implementation
std::vector<std::string> DrugExposure::validation_errors() const {
    std::vector<std::string> errors;
    
    if (drug_exposure_id <= 0) {
        errors.push_back("drug_exposure_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (drug_concept_id <= 0) {
        errors.push_back("drug_concept_id must be positive");
    }
    if (drug_type_concept_id <= 0) {
        errors.push_back("drug_type_concept_id must be positive");
    }
    if (drug_exposure_start_date > drug_exposure_end_date) {
        errors.push_back("drug_exposure_start_date must be before or equal to drug_exposure_end_date");
    }
    if (quantity.has_value() && !validate_quantity()) {
        errors.push_back("quantity must be positive when specified");
    }
    if (days_supply.has_value() && !validate_days_supply()) {
        errors.push_back("days_supply must be positive when specified");
    }
    
    return errors;
}

// ProcedureOccurrence validation_errors implementation
std::vector<std::string> ProcedureOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (procedure_occurrence_id <= 0) {
        errors.push_back("procedure_occurrence_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (procedure_concept_id <= 0) {
        errors.push_back("procedure_concept_id must be positive");
    }
    if (procedure_type_concept_id <= 0) {
        errors.push_back("procedure_type_concept_id must be positive");
    }
    if (procedure_end_date.has_value() && procedure_date > procedure_end_date.value()) {
        errors.push_back("procedure_date must be before or equal to procedure_end_date");
    }
    
    return errors;
}

// Measurement validation_errors implementation
std::vector<std::string> Measurement::validation_errors() const {
    std::vector<std::string> errors;
    
    if (measurement_id <= 0) {
        errors.push_back("measurement_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (measurement_concept_id <= 0) {
        errors.push_back("measurement_concept_id must be positive");
    }
    if (measurement_type_concept_id <= 0) {
        errors.push_back("measurement_type_concept_id must be positive");
    }
    if (!validate_ranges()) {
        errors.push_back("range_low must be less than or equal to range_high when both are specified");
    }
    
    return errors;
}

// Observation validation_errors implementation
std::vector<std::string> Observation::validation_errors() const {
    std::vector<std::string> errors;
    
    if (observation_id <= 0) {
        errors.push_back("observation_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (observation_concept_id <= 0) {
        errors.push_back("observation_concept_id must be positive");
    }
    if (observation_type_concept_id <= 0) {
        errors.push_back("observation_type_concept_id must be positive");
    }
    
    return errors;
}

// Death validation_errors implementation
std::vector<std::string> Death::validation_errors() const {
    std::vector<std::string> errors;
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (!validate_death_date()) {
        errors.push_back("death_date must be valid and not in the future");
    }
    
    return errors;
}

// Note validation_errors implementation
std::vector<std::string> Note::validation_errors() const {
    std::vector<std::string> errors;
    
    if (note_id <= 0) {
        errors.push_back("note_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (note_type_concept_id <= 0) {
        errors.push_back("note_type_concept_id must be positive");
    }
    if (note_class_concept_id <= 0) {
        errors.push_back("note_class_concept_id must be positive");
    }
    if (encoding_concept_id <= 0) {
        errors.push_back("encoding_concept_id must be positive");
    }
    if (language_concept_id <= 0) {
        errors.push_back("language_concept_id must be positive");
    }
    if (note_text.empty()) {
        errors.push_back("note_text cannot be empty");
    }
    
    return errors;
}

// Concept validation_errors implementation
std::vector<std::string> Concept::validation_errors() const {
    std::vector<std::string> errors;
    
    if (concept_id <= 0) {
        errors.push_back("concept_id must be positive");
    }
    if (concept_name.empty()) {
        errors.push_back("concept_name cannot be empty");
    }
    if (domain_id.empty()) {
        errors.push_back("domain_id cannot be empty");
    }
    if (vocabulary_id.empty()) {
        errors.push_back("vocabulary_id cannot be empty");  
    }
    if (concept_class_id.empty()) {
        errors.push_back("concept_class_id cannot be empty");
    }
    if (concept_code.empty()) {
        errors.push_back("concept_code cannot be empty");
    }
    if (valid_start_date > valid_end_date) {
        errors.push_back("valid_start_date must be before or equal to valid_end_date");
    }
    
    return errors;
}

// Location validation_errors implementation  
std::vector<std::string> Location::validation_errors() const {
    std::vector<std::string> errors;
    
    if (location_id <= 0) {
        errors.push_back("location_id must be positive");
    }
    if (zip.has_value() && !validate_uk_postcode()) {
        errors.push_back("UK postcode format is invalid");
    }
    
    return errors;
}

// CareSite validation_errors implementation
std::vector<std::string> CareSite::validation_errors() const {
    std::vector<std::string> errors;
    
    if (care_site_id <= 0) {
        errors.push_back("care_site_id must be positive");
    }
    
    return errors;
}

// Provider validation_errors implementation
std::vector<std::string> Provider::validation_errors() const {
    std::vector<std::string> errors;
    
    if (provider_id <= 0) {
        errors.push_back("provider_id must be positive");
    }
    
    return errors;
}

// VisitDetail validation_errors implementation
std::vector<std::string> VisitDetail::validation_errors() const {
    std::vector<std::string> errors;
    
    if (visit_detail_id <= 0) {
        errors.push_back("visit_detail_id must be positive");
    }
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    if (visit_detail_concept_id <= 0) {
        errors.push_back("visit_detail_concept_id must be positive");
    }
    if (visit_detail_type_concept_id <= 0) {
        errors.push_back("visit_detail_type_concept_id must be positive");
    }
    if (visit_occurrence_id <= 0) {
        errors.push_back("visit_occurrence_id must be positive");
    }
    if (visit_detail_start_date > visit_detail_end_date) {
        errors.push_back("visit_detail_start_date must be before or equal to visit_detail_end_date");
    }
    
    return errors;
}

} // namespace omop::cdm