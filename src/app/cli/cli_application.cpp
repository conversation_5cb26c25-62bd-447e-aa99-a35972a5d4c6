#include "cli_application.h"
#include "common/logging.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include "extract/database_connector.h"
#include "extract/csv_extractor.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include <iostream>
#include <cstdlib>
#include <csignal>

using namespace omop;

// Global flag for graceful shutdown
std::atomic<bool> g_shutdown_requested{false};

/**
 * @brief Signal handler for graceful shutdown
 */
void signal_handler(int signum) {
    if (signum == SIGINT || signum == SIGTERM) {
        std::cout << "\nShutdown requested..." << std::endl;
        g_shutdown_requested = true;
    }
}

/**
 * @brief Initialize the application
 */
void initialize_application() {
    // Set up signal handlers
    std::signal(SIGINT, signal_handler);
    std::signal(SIGTERM, signal_handler);

    // Initialize logging
    common::LoggingConfig::initialize_default();

    // Register component factories
    extract::PostgreSQLRegistrar::register_components();
    extract::CsvExtractorFactory::register_extractors();
    load::LoaderFactory::register_loaders();

    // Set up default logger
    auto logger = common::Logger::get("omop-etl");
    logger->info("OMOP ETL Pipeline initialized");
}

/**
 * @brief Clean up application resources
 */
void cleanup_application() {
    // Flush all logs
    common::LoggingConfig::flush_all();

    // Shutdown logging
    common::LoggingConfig::shutdown();
}

/**
 * @brief Main entry point
 */
int main(int argc, char* argv[]) {
    try {
        // Initialize application
        initialize_application();

        // Create and run CLI application
        cli::OmopEtlCli cli_app;
        int result = cli_app.run(argc, argv);

        // Clean up
        cleanup_application();

        return result;

    } catch (const common::OmopException& e) {
        std::cerr << "OMOP ETL Error: " << e.what() << std::endl;
        auto logger = common::Logger::get("omop-etl");
        logger->error("Fatal error: {}", e.what());
        cleanup_application();
        return EXIT_FAILURE;
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        auto logger = common::Logger::get("omop-etl");
        logger->critical("Unexpected error: {}", e.what());
        cleanup_application();
        return EXIT_FAILURE;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        cleanup_application();
        return EXIT_FAILURE;
    }
}

// CLI Implementation
namespace omop::cli {

OmopEtlCli::OmopEtlCli() {
    logger_ = common::Logger::get("omop-etl-cli");

    app_ = std::make_unique<CliApplication>(
        "omop-etl",
        "1.0.0",
        "OMOP CDM ETL Pipeline - Extract, Transform, and Load clinical data"
    );

    // Global options
    app_->add_global_option({
        .short_name = "c",
        .long_name = "config",
        .description = "Configuration file path",
        .default_value = "config/etl/config.yaml"
    });

    app_->add_global_option({
        .short_name = "v",
        .long_name = "verbose",
        .description = "Enable verbose output",
        .has_value = false
    });

    app_->add_global_option({
        .short_name = "q",
        .long_name = "quiet",
        .description = "Suppress output except errors",
        .has_value = false
    });

    // Extract command
    app_->add_command(Command("extract", "Extract data from source")
        .add_argument({
            .name = "source",
            .description = "Source table or file",
            .required = true
        })
        .add_option({
            .short_name = "t",
            .long_name = "type",
            .description = "Extractor type (database, csv, json)",
            .default_value = "database"
        })
        .add_option({
            .short_name = "o",
            .long_name = "output",
            .description = "Output file (optional)"
        })
        .set_handler([this](const auto& args) { return handle_extract(args); })
    );

    // Transform command
    app_->add_command(Command("transform", "Transform data to OMOP CDM format")
        .add_argument({
            .name = "table",
            .description = "Target OMOP table name",
            .required = true
        })
        .add_option({
            .short_name = "i",
            .long_name = "input",
            .description = "Input file (if not from database)"
        })
        .add_option({
            .short_name = "o",
            .long_name = "output",
            .description = "Output file (optional)"
        })
        .set_handler([this](const auto& args) { return handle_transform(args); })
    );

    // Load command
    app_->add_command(Command("load", "Load data into OMOP CDM database")
        .add_argument({
            .name = "table",
            .description = "Target OMOP table name",
            .required = true
        })
        .add_option({
            .short_name = "i",
            .long_name = "input",
            .description = "Input file to load"
        })
        .add_option({
            .short_name = "b",
            .long_name = "batch-size",
            .description = "Batch size for loading",
            .default_value = "1000"
        })
        .set_handler([this](const auto& args) { return handle_load(args); })
    );

    // Run command (full ETL pipeline)
    app_->add_command(Command("run", "Run complete ETL pipeline")
        .add_argument({
            .name = "table",
            .description = "OMOP table to process",
            .required = true
        })
        .add_option({
            .short_name = "s",
            .long_name = "source",
            .description = "Source table/file override"
        })
        .add_option({
            .short_name = "j",
            .long_name = "jobs",
            .description = "Number of parallel jobs",
            .default_value = "1"
        })
        .add_option({
            .long_name = "dry-run",
            .description = "Perform dry run without loading",
            .has_value = false
        })
        .set_handler([this](const auto& args) { return handle_run(args); })
    );

    // Validate command
    app_->add_command(Command("validate", "Validate configuration or data")
        .add_argument({
            .name = "target",
            .description = "What to validate (config, mapping, data)",
            .required = true
        })
        .add_option({
            .short_name = "t",
            .long_name = "table",
            .description = "Table to validate (for mapping/data)"
        })
        .set_handler([this](const auto& args) { return handle_validate(args); })
    );

    // List commands
    app_->add_command(Command("list-tables", "List supported OMOP tables")
        .set_handler([this](const auto& args) { return handle_list_tables(args); })
    );

    app_->add_command(Command("list-mappings", "List configured table mappings")
        .set_handler([this](const auto& args) { return handle_list_mappings(args); })
    );

    // Server command
    app_->add_command(Command("server", "Start REST API server")
        .add_option({
            .short_name = "p",
            .long_name = "port",
            .description = "Server port",
            .default_value = "8080"
        })
        .add_option({
            .short_name = "h",
            .long_name = "host",
            .description = "Server host",
            .default_value = "0.0.0.0"
        })
        .set_handler([this](const auto& args) { return handle_server(args); })
    );

    // Utility commands
    app_->add_command(Command("test-connection", "Test database connection")
        .add_argument({
            .name = "database",
            .description = "Database to test (source or target)",
            .required = true,
            .allowed_values = {"source", "target"}
        })
        .set_handler([this](const auto& args) { return handle_test_connection(args); })
    );

    app_->add_command(Command("create-schema", "Create OMOP CDM schema")
        .add_option({
            .long_name = "drop-existing",
            .description = "Drop existing schema first",
            .has_value = false
        })
        .add_option({
            .long_name = "tables",
            .description = "Comma-separated list of tables to create"
        })
        .set_handler([this](const auto& args) { return handle_create_schema(args); })
    );
}

int OmopEtlCli::run(int argc, char* argv[]) {
    return app_->run(argc, argv);
}

int OmopEtlCli::handle_run(const std::unordered_map<std::string, std::string>& args) {
    // Load configuration
    if (!load_configuration(args.at("config"))) {
        return EXIT_FAILURE;
    }

    const std::string& table_name = args.at("table");
    logger_->info("Starting ETL pipeline for table: {}", table_name);

    // Get table mapping
    auto mapping = config_->get_table_mapping(table_name);
    if (!mapping) {
        logger_->error("No mapping found for table: {}", table_name);
        std::cerr << "Error: No mapping configured for table '" << table_name << "'" << std::endl;
        return EXIT_FAILURE;
    }

    try {
        // Build pipeline
        auto pipeline = build_pipeline(table_name, args);

        // Set up progress callback
        bool verbose = args.count("verbose") > 0;
        bool quiet = args.count("quiet") > 0;

        if (!quiet) {
            pipeline->register_progress_callback([verbose](const core::PipelineExecutionStats& stats) {
                if (verbose) {
                    std::cout << "\rProcessed: " << stats.successful_records
                             << "/" << stats.total_records_processed
                             << " (" << std::fixed << std::setprecision(1)
                             << (stats.total_records_processed > 0 ? 
                                 (static_cast<double>(stats.successful_records) / stats.total_records_processed * 100) : 0.0)
                             << "%)"
                             << " Errors: " << stats.failed_records
                             << " Time: " << std::chrono::duration_cast<std::chrono::seconds>(
                                 stats.end_time - stats.start_time).count() << "s"
                             << std::flush;
                } else {
                    static ProgressBar progress_bar(stats.total_records_processed);
                    progress_bar.update(stats.successful_records);
                }
            });
        }

        // Check for dry run
        bool dry_run = args.count("dry-run") > 0;
        if (dry_run) {
            logger_->info("Running in dry-run mode - no data will be loaded");
            // TODO: Implement dry-run mode
        }

        // Start pipeline
        std::string job_id = "etl_" + table_name + "_" +
                           std::to_string(std::chrono::system_clock::now().time_since_epoch().count());

        pipeline->start();

        // Wait for completion or shutdown
        while (pipeline->get_status() == core::PipelineStatus::Running) {
            if (g_shutdown_requested) {
                logger_->info("Stopping pipeline due to shutdown request");
                pipeline->stop();
                break;
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        // Get results
        auto execution_stats = pipeline->get_execution_stats();

        if (!quiet) {
            std::cout << std::endl;
            std::cout << "ETL Pipeline completed:" << std::endl;
            std::cout << "  Total records: " << execution_stats.total_records_processed << std::endl;
            std::cout << "  Processed: " << execution_stats.successful_records << std::endl;
            std::cout << "  Errors: " << execution_stats.failed_records << std::endl;
            std::cout << "  Duration: " << std::chrono::duration_cast<std::chrono::seconds>(
                execution_stats.end_time - execution_stats.start_time).count() << " seconds" << std::endl;

            if (execution_stats.failed_records > 0) {
                std::cout << "  Error rate: " << std::fixed << std::setprecision(2)
                         << (execution_stats.total_records_processed > 0 ? 
                             (static_cast<double>(execution_stats.failed_records) / execution_stats.total_records_processed * 100) : 0.0)
                         << "%" << std::endl;
            }
        }

        return execution_stats.status == core::PipelineStatus::Completed ? EXIT_SUCCESS : EXIT_FAILURE;

    } catch (const std::exception& e) {
        logger_->error("Pipeline failed: {}", e.what());
        std::cerr << "Error: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
}

bool OmopEtlCli::load_configuration(const std::string& config_file) {
    try {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config(config_file);
        logger_->info("Configuration loaded from: {}", config_file);
        return true;
    } catch (const common::ConfigurationException& e) {
        logger_->error("Failed to load configuration: {}", e.what());
        std::cerr << "Configuration error: " << e.what() << std::endl;
        return false;
    }
}

std::unique_ptr<core::ETLPipeline> OmopEtlCli::build_pipeline(
    const std::string& table_name,
    const std::unordered_map<std::string, std::string>& options) {

    auto builder = core::PipelineBuilder();

    // Configure pipeline
    core::PipelineConfig pipeline_config;
    pipeline_config.batch_size = config_->get_value_or<size_t>("etl_settings.batch_size", 1000);
    pipeline_config.max_parallel_batches = std::stoul(options.count("jobs") ? options.at("jobs") : "1");
    pipeline_config.error_threshold = config_->get_value_or<double>("etl_settings.error_threshold", 0.01);
    pipeline_config.validate_records = config_->get_value_or<bool>("etl_settings.validation_mode", true);

    builder.with_config(pipeline_config);

    // Set up extractor
    auto source_db_config = config_->get_source_db();
    auto extractor_params = std::unordered_map<std::string, std::any>{
        {"table_name", std::any(options.count("source") ? options.at("source") :
                               config_->get_table_mapping(table_name)->source_table())}
    };

    builder.with_extractor("database", extractor_params);

    // Set up transformer
    builder.with_transformer_for_table(table_name);

    // Set up loader
    auto target_db_config = config_->get_target_db();
    auto loader_params = std::unordered_map<std::string, std::any>{
        {"table_name", std::any(table_name)}
    };

    builder.with_loader("omop_database", loader_params);

    return builder.build();
}

} // namespace omop::cli