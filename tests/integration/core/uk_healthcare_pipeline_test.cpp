/**
 * @file test_uk_localized_pipeline_integration.cpp
 * @brief UK-localized integration tests for OMOP ETL pipeline core components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains comprehensive integration tests for the core pipeline
 * with UK healthcare localization including NHS number formats, UK date/time,
 * British currency, and UK postal codes.
 */

#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "core/encoding.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <memory>
#include <thread>
#include <chrono>
#include <locale>
#include <iomanip>
#include <sstream>
#include <random>

namespace omop::core::test {

/**
 * @brief UK healthcare test fixture with proper localization
 */
class UKPipelineIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        // Initialize UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_time_format_ = "%H:%M";
        uk_decimal_separator_ = ".";
        uk_thousands_separator_ = ",";
        
        // UK healthcare context dates
        uk_start_date_ = std::chrono::system_clock::now() - std::chrono::hours(72);
        uk_end_date_ = std::chrono::system_clock::now() - std::chrono::hours(24);
        uk_current_time_ = std::chrono::system_clock::now();
        
        // Initialize logging for UK healthcare
        common::LoggingConfig::initialize_default();
        
        // Create pipeline with UK-specific configuration
        PipelineConfig config;
        config.batch_size = 100;  // Suitable for UK NHS data processing
        config.error_threshold = 0.02; // 2% error threshold for UK healthcare quality
        config.validate_records = true;
        pipeline_ = std::make_unique<ETLPipeline>(config);
    }

    void TearDown() override {
        pipeline_.reset();
    }

    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }

    // Helper to format UK temperature (Celsius)
    std::string formatUKTemperature(double celsius) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << celsius << "°C";
        return oss.str();
    }

    // Helper to generate UK NHS number
    std::string generateNHSNumber(int index) {
        // NHS number format: ABC-DEF-GHIJ (10 digits with check digit)
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(3) << (450 + index % 100)
            << std::setw(3) << (557 + index % 100)  
            << std::setw(4) << (7100 + index);
        return oss.str();
    }

    // Helper to generate UK postcode
    std::string generateUKPostcode(int index) {
        std::vector<std::string> uk_postcodes = {
            "SW1A 1AA", "W1A 0AX", "M1 1AA", "B33 8TH", "WV99 1AA",
            "L1 8JQ", "S1 2HE", "NE1 4ST", "CF10 3AT", "EH1 2NG",
            "BT1 5GS", "G1 1RE", "CB2 1TN", "OX1 2JD", "SE1 7JN"
        };
        return uk_postcodes[index % uk_postcodes.size()];
    }

    std::unique_ptr<ETLPipeline> pipeline_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_time_format_;
    std::string uk_decimal_separator_;
    std::string uk_thousands_separator_;
    std::chrono::system_clock::time_point uk_start_date_;
    std::chrono::system_clock::time_point uk_end_date_;
    std::chrono::system_clock::time_point uk_current_time_;
};

/**
 * @brief UK NHS data extractor for testing
 */
class UKNHSMockExtractor : public IExtractor {
public:
    UKNHSMockExtractor(size_t total_patients = 1000)
        : total_patients_(total_patients), current_position_(0) {}

    void initialize(const std::unordered_map<std::string, std::any>& config,
                   ProcessingContext& context) override {
        initialized_ = true;
        batch_size_ = 50; // Suitable for UK NHS data processing
        if (config.find("batch_size") != config.end()) {
            batch_size_ = std::any_cast<size_t>(config.at("batch_size"));
        }
        context.log("info", "Initialized UK NHS data extractor");
    }

    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        size_t records_to_extract = std::min(batch_size, total_patients_ - current_position_);

        for (size_t i = 0; i < records_to_extract; ++i) {
            Record patient;
            size_t patient_index = current_position_ + i;
            
            // UK patient data with proper NHS format
            patient.setField("nhs_number", generateNHSNumber(patient_index));
            patient.setField("patient_id", static_cast<int64_t>(patient_index + 1));
            patient.setField("forename", generateUKForename(patient_index));
            patient.setField("surname", generateUKSurname(patient_index));
            patient.setField("date_of_birth", generateUKDateOfBirth(patient_index));
            patient.setField("postcode", generateUKPostcode(patient_index));
            patient.setField("gp_practice_code", generateGPPracticeCode(patient_index));
            
            // UK-specific clinical data
            patient.setField("height_cm", 150.0 + (patient_index % 50)); // Height in cm
            patient.setField("weight_kg", 50.0 + (patient_index % 80));   // Weight in kg
            patient.setField("temperature_celsius", 36.0 + (patient_index % 3) * 0.5); // Body temp in Celsius
            patient.setField("blood_pressure_systolic", 110 + (patient_index % 40));
            patient.setField("blood_pressure_diastolic", 70 + (patient_index % 20));
            
            // UK currency amounts (prescription costs, etc.)
            patient.setField("prescription_cost_gbp", (patient_index % 50) * 1.50); // In British pounds
            
            // Set UK extraction timestamp
            patient.setField("extraction_timestamp", std::chrono::system_clock::now());
            
            batch.addRecord(std::move(patient));
        }

        current_position_ += records_to_extract;
        context.log("info", "Extracted " + std::to_string(records_to_extract) + " UK NHS patient records");
        return batch;
    }

    bool has_more_data() const override {
        return current_position_ < total_patients_;
    }

    std::string get_type() const override { return "uk_nhs_mock"; }

    void finalize(ProcessingContext& context) override {
        finalized_ = true;
        context.log("info", "Finalized UK NHS data extraction");
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"total_patients", total_patients_},
            {"extracted", current_position_},
            {"initialized", initialized_},
            {"finalized", finalized_},
            {"extraction_type", std::string("UK_NHS")}
        };
    }

private:
    std::string generateNHSNumber(size_t index) {
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(10) << (4505577100ULL + index);
        return oss.str();
    }

    std::string generateUKForename(size_t index) {
        std::vector<std::string> uk_forenames = {
            "Oliver", "Amelia", "George", "Isla", "Arthur", "Ava", "Noah", "Mia",
            "Muhammad", "Sophia", "Leo", "Grace", "Harry", "Lily", "Oscar", "Emily",
            "Archie", "Olivia", "Henry", "Ella", "Theodore", "Freya", "Jack", "Florence"
        };
        return uk_forenames[index % uk_forenames.size()];
    }

    std::string generateUKSurname(size_t index) {
        std::vector<std::string> uk_surnames = {
            "Smith", "Jones", "Taylor", "Brown", "Williams", "Wilson", "Johnson",
            "Davies", "Robinson", "Wright", "Thompson", "Evans", "Walker", "White",
            "Roberts", "Green", "Hall", "Wood", "Jackson", "Clarke"
        };
        return uk_surnames[index % uk_surnames.size()];
    }

    std::string generateUKDateOfBirth(size_t index) {
        // Generate dates in UK format (DD/MM/YYYY)
        int year = 1950 + (index % 70);
        int month = 1 + (index % 12);
        int day = 1 + (index % 28);
        
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(2) << day << "/"
            << std::setw(2) << month << "/" << year;
        return oss.str();
    }

    std::string generateUKPostcode(size_t index) {
        std::vector<std::string> uk_postcodes = {
            "SW1A 1AA", "W1A 0AX", "M1 1AA", "B33 8TH", "WV99 1AA",
            "L1 8JQ", "S1 2HE", "NE1 4ST", "CF10 3AT", "EH1 2NG"
        };
        return uk_postcodes[index % uk_postcodes.size()];
    }

    std::string generateGPPracticeCode(size_t index) {
        // UK GP practice codes (usually 6 characters)
        std::ostringstream oss;
        oss << "P" << std::setfill('0') << std::setw(5) << (10000 + index % 50000);
        return oss.str();
    }

    size_t total_patients_;
    size_t current_position_;
    size_t batch_size_{50};
    bool initialized_{false};
    bool finalized_{false};
};

/**
 * @brief UK OMOP CDM transformer for testing
 */
class UKOMOPTransformer : public ITransformer {
public:
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   ProcessingContext& context) override {
        initialized_ = true;
        context.log("info", "Initialized UK OMOP CDM transformer");
        
        // UK-specific concept mappings
        if (config.find("use_uk_concepts") != config.end()) {
            use_uk_concepts_ = std::any_cast<bool>(config.at("use_uk_concepts"));
        }
    }

    std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
        Record omop_record = record;

        try {
            // Transform to OMOP CDM format with UK localization
            
            // Convert NHS number to person_source_value
            auto nhs_number = record.getFieldAs<std::string>("nhs_number");
            omop_record.setField("person_source_value", "NHS-" + nhs_number);
            
            // Set UK-specific concept IDs
            omop_record.setField("location_concept_id", 8551); // United Kingdom
            omop_record.setField("ethnicity_concept_id", 38003564); // Not Hispanic or Latino
            
            // Convert UK measurements to OMOP standard units
            if (record.hasField("temperature_celsius")) {
                auto temp_c = record.getFieldAs<double>("temperature_celsius");
                omop_record.setField("temperature_value", temp_c);
                omop_record.setField("temperature_unit_concept_id", 8653); // Celsius
                omop_record.setField("temperature_source_value", std::to_string(temp_c) + "°C");
            }
            
            // Convert UK currency to standard format
            if (record.hasField("prescription_cost_gbp")) {
                auto cost_gbp = record.getFieldAs<double>("prescription_cost_gbp");
                omop_record.setField("cost_value", cost_gbp);
                omop_record.setField("cost_currency_concept_id", 44818668); // British Pound
                omop_record.setField("cost_source_value", "£" + std::to_string(cost_gbp));
            }
            
            // Set UK transformation metadata
            omop_record.setField("transformed_at", std::chrono::system_clock::now());
            omop_record.setField("transformation_version", "UK-OMOP-v1.0");
            omop_record.setField("source_system", "UK-NHS");
            
            transformed_count_++;
            
            if (transformed_count_ % 100 == 0) {
                context.log("info", "Transformed " + std::to_string(transformed_count_) + " UK NHS records to OMOP CDM");
            }
            
            return omop_record;
            
        } catch (const std::exception& e) {
            context.log("error", "Failed to transform UK NHS record: " + std::string(e.what()));
            context.increment_errors();
            return std::nullopt;
        }
    }

    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        RecordBatch transformed_batch;

        for (const auto& record : batch) {
            auto result = transform(record, context);
            if (result) {
                transformed_batch.addRecord(std::move(*result));
            }
        }

        return transformed_batch;
    }

    std::string get_type() const override { return "uk_omop_cdm"; }

    omop::common::ValidationResult validate(const Record& record) const override {
        omop::common::ValidationResult result;

        // UK-specific validation rules
        if (!record.hasField("nhs_number")) {
            result.add_error("nhs_number", "Missing required NHS number", "required");
        } else {
            auto nhs_number = record.getFieldAs<std::string>("nhs_number");
            if (nhs_number.length() != 10) {
                result.add_error("nhs_number", "Invalid NHS number format (must be 10 digits)", "format");
            }
        }

        if (!record.hasField("postcode")) {
            result.add_error("postcode", "Missing required UK postcode", "required");
        }

        return result;
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"transformed_count", static_cast<size_t>(transformed_count_)},
            {"initialized", initialized_},
            {"use_uk_concepts", use_uk_concepts_},
            {"transformer_type", std::string("UK_OMOP_CDM")}
        };
    }

private:
    std::atomic<size_t> transformed_count_{0};
    bool initialized_{false};
    bool use_uk_concepts_{true};
};

/**
 * @brief UK healthcare data loader for testing
 */
class UKHealthcareLoader : public ILoader {
public:
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   ProcessingContext& context) override {
        initialized_ = true;
        context.log("info", "Initialized UK healthcare data loader");
        
        if (config.find("target_database") != config.end()) {
            target_database_ = std::any_cast<std::string>(config.at("target_database"));
        }
    }

    bool load(const Record& record, ProcessingContext& context) override {
        try {
            // Simulate loading UK healthcare data with proper validation
            if (!record.hasField("person_source_value")) {
                context.log("error", "Cannot load record without person_source_value");
                return false;
            }
            
            loaded_records_.push_back(record);
            loaded_count_++;
            
            // Simulate UK database constraints
            if (loaded_count_ % 1000 == 0) {
                context.log("info", "Loaded " + std::to_string(loaded_count_) + " UK healthcare records");
            }
            
            return true;
            
        } catch (const std::exception& e) {
            context.log("error", "Failed to load UK healthcare record: " + std::string(e.what()));
            context.increment_errors();
            return false;
        }
    }

    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        size_t loaded = 0;

        for (const auto& record : batch) {
            if (load(record, context)) {
                loaded++;
            }
        }

        return loaded;
    }

    void commit(ProcessingContext& context) override {
        commit_count_++;
        context.log("info", "Committed UK healthcare data batch #" + std::to_string(commit_count_));
    }

    void rollback(ProcessingContext& context) override {
        rollback_count_++;
        loaded_records_.clear();
        loaded_count_ = 0;
        context.log("warn", "Rolled back UK healthcare data due to error");
    }

    std::string get_type() const override { return "uk_healthcare"; }

    void finalize(ProcessingContext& context) override {
        finalized_ = true;
        context.log("info", "Finalized UK healthcare data loading: " + std::to_string(loaded_count_) + " records");
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"loaded_count", static_cast<size_t>(loaded_count_)},
            {"commit_count", static_cast<size_t>(commit_count_)},
            {"rollback_count", static_cast<size_t>(rollback_count_)},
            {"initialized", initialized_},
            {"finalized", finalized_},
            {"target_database", target_database_},
            {"loader_type", std::string("UK_HEALTHCARE")}
        };
    }

    const std::vector<Record>& get_loaded_records() const { return loaded_records_; }

private:
    std::vector<Record> loaded_records_;
    std::atomic<size_t> loaded_count_{0};
    std::atomic<size_t> commit_count_{0};
    std::atomic<size_t> rollback_count_{0};
    bool initialized_{false};
    bool finalized_{false};
    std::string target_database_{"UK_OMOP_CDM"};
};

// Test UK NHS data pipeline with proper localization
TEST_F(UKPipelineIntegrationTest, UKNHSDataPipelineProcessesCorrectly) {
    // Set up UK NHS data pipeline components
    auto extractor = std::make_unique<UKNHSMockExtractor>(500);
    auto transformer = std::make_unique<UKOMOPTransformer>();
    auto loader = std::make_unique<UKHealthcareLoader>();

    // Store raw pointers for verification
    auto* loader_ptr = dynamic_cast<UKHealthcareLoader*>(loader.get());

    // Configure pipeline with UK settings
    std::unordered_map<std::string, std::any> transformer_config = {
        {"use_uk_concepts", true}
    };
    std::unordered_map<std::string, std::any> loader_config = {
        {"target_database", std::string("UK_OMOP_CDM")}
    };

    ProcessingContext ctx;
    transformer->initialize(transformer_config, ctx);
    loader->initialize(loader_config, ctx);

    auto test_pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    // Start UK NHS data pipeline
    test_pipeline->start();
    auto status = test_pipeline->wait_for_completion();
    auto execution_stats = test_pipeline->get_execution_stats();

    // Verify UK NHS pipeline results
    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);
    EXPECT_EQ(execution_stats.successful_records, 500); // Pipeline processes 500 records (same as extractor)
    EXPECT_EQ(execution_stats.failed_records, 0);
    EXPECT_GT(execution_stats.total_duration.count(), 0);

    // Verify UK healthcare data was loaded correctly - loader shows 500 distinct records
    EXPECT_EQ(loader_ptr->get_loaded_records().size(), 500);
    
    // Verify UK-specific fields are present
    const auto& first_record = loader_ptr->get_loaded_records()[0];
    EXPECT_TRUE(first_record.hasField("person_source_value"));
    EXPECT_TRUE(first_record.hasField("temperature_unit_concept_id"));
    EXPECT_TRUE(first_record.hasField("cost_currency_concept_id"));
    
    // Check NHS number format
    auto person_source = first_record.getFieldAs<std::string>("person_source_value");
    EXPECT_TRUE(person_source.find("NHS-") == 0);
}

// Test UK text encoding processing with proper character handling
TEST_F(UKPipelineIntegrationTest, UKTextEncodingHandlesSpecialCharactersCorrectly) {
    auto encoder = create_text_encoder();
    EncodingConfig config;
    config.source_encoding = Encoding::UTF8;
    config.target_encoding = Encoding::UTF8;
    encoder->initialize(config);

    // Test UK-specific text with special characters
    std::string uk_text = "Patient: Seán O'Brien from Coöperative Street, Cardiff (£125.50 prescription)";
    std::string cleaned = encoder->clean_invalid_chars(uk_text);
    EXPECT_FALSE(cleaned.empty());
    
    // Test UK currency normalization
    std::string normalized = encoder->normalize_text("Patient cost: £250.75", "NFKC");
    EXPECT_TRUE(normalized.find("GBP") != std::string::npos);
    
    // Test UK temperature normalization
    std::string temp_text = "Body temperature: 37.2℃";
    std::string normalized_temp = encoder->normalize_text(temp_text, "NFKC");
    EXPECT_TRUE(normalized_temp.find("°C") != std::string::npos);
}

// Test UK pipeline with batch processing optimized for NHS data volumes
TEST_F(UKPipelineIntegrationTest, UKBatchProcessingHandlesNHSDataVolumesEfficiently) {
    PipelineConfig config;
    config.batch_size = 100;  // Optimized for UK NHS data processing
    config.max_parallel_batches = 4;
    config.error_threshold = 0.01; // 1% error threshold for UK healthcare

    auto test_pipeline = PipelineBuilder()
        .with_extractor(std::make_unique<UKNHSMockExtractor>(2000))
        .with_transformer(std::make_unique<UKOMOPTransformer>())
        .with_loader(std::make_unique<UKHealthcareLoader>())
        .build();

    [[maybe_unused]] auto* loader_ptr = dynamic_cast<UKHealthcareLoader*>(test_pipeline.get());

    test_pipeline->start();
    auto status = test_pipeline->wait_for_completion();
    auto execution_stats = test_pipeline->get_execution_stats();

    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);
    EXPECT_EQ(execution_stats.successful_records, 2000); // Pipeline processes 2000 records (same as extractor)

    // Verify UK NHS batch processing efficiency
    auto stats = loader_ptr->get_statistics();
    size_t commit_count = std::any_cast<size_t>(stats["commit_count"]);
    EXPECT_GE(commit_count, 1); // At least one commit occurred
    EXPECT_LE(commit_count, 10); // Reasonable number of commits for efficiency
}

// Test UK currency and measurement conversion in pipeline
TEST_F(UKPipelineIntegrationTest, UKMeasurementConversionWorksCorrectlyInPipeline) {
    auto extractor = std::make_unique<UKNHSMockExtractor>(100);
    auto transformer = std::make_unique<UKOMOPTransformer>();
    auto loader = std::make_unique<UKHealthcareLoader>();

    [[maybe_unused]] auto* loader_ptr = loader.get();

    auto test_pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    test_pipeline->start();
    auto status = test_pipeline->wait_for_completion();
    auto execution_stats = test_pipeline->get_execution_stats();

    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);

    // Verify UK measurement conversions
    const auto& records = loader_ptr->get_loaded_records();
    EXPECT_FALSE(records.empty());

    for (const auto& record : records) {
        // Check temperature is in Celsius (UK standard)
        if (record.hasField("temperature_unit_concept_id")) {
            auto unit_concept = record.getFieldAs<int32_t>("temperature_unit_concept_id");
            EXPECT_EQ(unit_concept, 8653); // Celsius concept ID
        }

        // Check currency is in British Pounds
        if (record.hasField("cost_currency_concept_id")) {
            auto currency_concept = record.getFieldAs<int32_t>("cost_currency_concept_id");
            EXPECT_EQ(currency_concept, 44818668); // British Pound concept ID
        }
    }
}

// Test UK error handling with NHS-specific validation rules
TEST_F(UKPipelineIntegrationTest, UKValidationRulesEnforceNHSDataQuality) {
    PipelineConfig config;
    config.stop_on_error = false;
    config.error_threshold = 0.05; // 5% error threshold for UK healthcare quality

    auto pipeline = std::make_unique<ETLPipeline>(config);

    // Create components that will produce some validation errors
    auto extractor = std::make_unique<UKNHSMockExtractor>(200);
    auto transformer = std::make_unique<UKOMOPTransformer>();
    auto loader = std::make_unique<UKHealthcareLoader>();

    [[maybe_unused]] auto* loader_ptr = loader.get();

    auto test_pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    test_pipeline->start();
    auto status = test_pipeline->wait_for_completion();
    auto execution_stats = test_pipeline->get_execution_stats();

    // Pipeline should complete with high data quality (low error rate)
    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);
    EXPECT_LE(execution_stats.failed_records / static_cast<double>(execution_stats.total_records_processed), 0.05); // Should meet UK healthcare quality standards
    
    // Verify most records were processed successfully
    EXPECT_GE(execution_stats.successful_records, 190); // At least 95% success rate
}

} // namespace omop::core::test