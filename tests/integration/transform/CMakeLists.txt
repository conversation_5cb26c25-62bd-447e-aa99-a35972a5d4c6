# Transform integration tests
set(TRANSFORM_INTEGRATION_TEST_SOURCES
    complex_data_transformation_test.cpp
    custom_transformation_rules_test.cpp
    extended_custom_transformation_test.cpp
    field_mapping_transformation_test.cpp
    transformation_engine_execution_test.cpp
    vocabulary_mapping_service_test.cpp
    validation_engine_integration_test.cpp
    transformation_registry_management_test.cpp
    transformation_cache_performance_test.cpp
    transformation_edge_cases_test.cpp
    uk_healthcare_transformation_test.cpp
)

add_executable(transform_integration_tests ${TRANSFORM_INTEGRATION_TEST_SOURCES})

target_link_libraries(transform_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(transform_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

# Link additional libraries for custom transformations
target_link_libraries(transform_integration_tests
    PRIVATE
        dl  # For plugin loading
)

add_test(
    NAME transform_integration_tests
    COMMAND transform_integration_tests
)

set_tests_properties(transform_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;transform"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)