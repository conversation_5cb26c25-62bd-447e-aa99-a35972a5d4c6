// Integration test for complex ETL workflows involving multiple data sources and transformations
#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "load/database_loader.h"
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <filesystem>
#include <thread>
#include <chrono>

namespace omop::test::integration {

/**
 * @brief Complex ETL workflow integration test
 *
 * Tests multi-source ETL scenarios with complex transformations
 * and parallel processing.
 */
class ComplexETLWorkflowTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize services
        setupVocabularyService();
        setupTransformationEngine();
        createTestDataFiles();
    }

    void TearDown() override {
        cleanupTestDataFiles();
        DatabaseFixture::TearDown();
    }

    void setupVocabularyService() {
        auto conn = createConnection("cdm");
        vocabulary_service_ = std::make_unique<transform::VocabularyService>(std::move(conn));
        vocabulary_service_->initialize();

        // Load test vocabulary mappings
        vocabulary_service_->load_mappings_from_db("vocabulary_mappings");
    }

    void setupTransformationEngine() {
        // Register custom transformations
        transform::TransformationRegistry::instance().register_transformation(
            "custom_age_calculation",
            [](const std::unordered_map<std::string, std::any>& params) {
                return std::make_unique<AgeCalculationTransformation>();
            });
    }

    void createTestDataFiles() {
        test_data_dir_ = std::filesystem::temp_directory_path() / "omop_test_data";
        std::filesystem::create_directories(test_data_dir_);

        // Create test files
        createPatientDataFile();
        createLabResultsFile();
        createClinicalNotesFile();
    }

    void cleanupTestDataFiles() {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
    }

    void createPatientDataFile() {
        std::ofstream file(test_data_dir_ / "patients_complex.csv");
        file << "patient_id,birth_date,gender,race,ethnicity,zip_code,deceased,death_date\n";
        file << "P001,1950-05-15,M,White,Not Hispanic,12345,N,\n";
        file << "P002,1965-08-22,F,Black,Hispanic,54321,Y,2023-12-01\n";
        file << "P003,1978-03-10,M,Asian,Not Hispanic,67890,N,\n";
    }

    void createLabResultsFile() {
        nlohmann::json lab_data = {
            {"results", {
                {
                    {"patient_id", "P001"},
                    {"lab_date", "2024-01-15T10:30:00"},
                    {"tests", {
                        {{"code", "GLU"}, {"value", 95}, {"unit", "mg/dL"}},
                        {{"code", "HGB"}, {"value", 14.5}, {"unit", "g/dL"}},
                        {{"code", "WBC"}, {"value", 7.2}, {"unit", "K/uL"}}
                    }}
                },
                {
                    {"patient_id", "P002"},
                    {"lab_date", "2024-01-20T14:45:00"},
                    {"tests", {
                        {{"code", "GLU"}, {"value", 180}, {"unit", "mg/dL"}},
                        {{"code", "A1C"}, {"value", 8.5}, {"unit", "%"}}
                    }}
                }
            }}
        };

        std::ofstream file(test_data_dir_ / "lab_results.json");
        file << lab_data.dump(2);
    }

    void createClinicalNotesFile() {
        std::ofstream file(test_data_dir_ / "clinical_notes.jsonl");
        file << R"({"patient_id":"P001","note_date":"2024-01-15","note_type":"Progress Note","text":"Patient presents with controlled hypertension. BP 130/85."})" << "\n";
        file << R"({"patient_id":"P002","note_date":"2024-01-20","note_type":"Discharge Summary","text":"Patient admitted with diabetic complications. Glucose control improved."})" << "\n";
    }

protected:
    std::filesystem::path test_data_dir_;
    std::unique_ptr<transform::VocabularyService> vocabulary_service_;

    /**
     * @brief Custom age calculation transformation for testing
     */
    class AgeCalculationTransformation : public transform::FieldTransformation {
    public:
        std::any transform(const std::any& input, core::ProcessingContext& context) override {
            if (input.type() == typeid(std::string)) {
                auto birth_date = std::any_cast<std::string>(input);
                // Simple age calculation (production would use proper date parsing)
                return 2024 - std::stoi(birth_date.substr(0, 4));
            }
            return input;
        }

        bool validate_input(const std::any& input) const override {
            return input.has_value() && input.type() == typeid(std::string);
        }

        std::string get_type() const override { return "custom_age_calculation"; }

        void configure(const YAML::Node& params) override {}
    };
};

// Test multi-source data integration with different formats
TEST_F(ComplexETLWorkflowTest, TestMultiSourceIntegration) {
    auto config_manager = std::make_shared<common::ConfigurationManager>();
    auto pipeline_manager = std::make_shared<core::PipelineManager>();
    auto etl_service = std::make_shared<service::ETLService>(config_manager, pipeline_manager);

    // Configure pipeline for patient data (CSV)
    core::PipelineConfig patient_pipeline_config;
    patient_pipeline_config.batch_size = 100;
    patient_pipeline_config.validate_records = true;

    auto patient_pipeline = std::make_unique<core::ETLPipeline>(patient_pipeline_config);

    // Set up CSV extractor
    auto csv_extractor = std::make_unique<extract::CsvExtractor>();
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = (test_data_dir_ / "patients_complex.csv").string();

    core::ProcessingContext context;
    csv_extractor->initialize(csv_config, context);
    patient_pipeline->set_extractor(std::move(csv_extractor));

    // Set up transformer with age calculation
    auto transformer = std::make_unique<transform::TransformationEngine>();
    transformer->register_transformation("age_calc",
        []() { return std::make_unique<AgeCalculationTransformation>(); });
    patient_pipeline->set_transformer(std::move(transformer));

    // Set up database loader
    auto db_loader = std::make_unique<load::DatabaseLoader>(
        createConnection("cdm"), load::DatabaseLoaderOptions{});
    patient_pipeline->set_loader(std::move(db_loader));

    // Execute patient pipeline
    auto patient_job = patient_pipeline->start("patient_import_001");
    auto patient_result = patient_job.get();

    EXPECT_EQ(patient_result.status, core::JobStatus::Completed);
    EXPECT_EQ(patient_result.processed_records, 3);
    EXPECT_EQ(patient_result.error_records, 0);
}

// Test parallel processing of multiple data sources
TEST_F(ComplexETLWorkflowTest, TestParallelSourceProcessing) {
    std::vector<std::future<core::JobInfo>> job_futures;

    // Process multiple sources in parallel
    auto process_source = [this](const std::string& source_file, const std::string& job_id) {
        return std::async(std::launch::async, [this, source_file, job_id]() {
            core::PipelineConfig config;
            config.batch_size = 50;

            auto pipeline = std::make_unique<core::ETLPipeline>(config);

            // Configure based on file type
            if (source_file.ends_with(".csv")) {
                auto extractor = std::make_unique<extract::CsvExtractor>();
                std::unordered_map<std::string, std::any> extract_config;
                extract_config["filepath"] = (test_data_dir_ / source_file).string();

                core::ProcessingContext context;
                extractor->initialize(extract_config, context);
                pipeline->set_extractor(std::move(extractor));
            } else if (source_file.ends_with(".json")) {
                auto extractor = std::make_unique<extract::JsonExtractor>();
                std::unordered_map<std::string, std::any> extract_config;
                extract_config["filepath"] = (test_data_dir_ / source_file).string();
                extract_config["root_path"] = "results";

                core::ProcessingContext context;
                extractor->initialize(extract_config, context);
                pipeline->set_extractor(std::move(extractor));
            }

            // Set up transformer and loader
            pipeline->set_transformer(std::make_unique<transform::TransformationEngine>());
            pipeline->set_loader(std::make_unique<load::DatabaseLoader>(
                createConnection("cdm"), load::DatabaseLoaderOptions{}));

            pipeline->start();
            auto execution_stats = pipeline->get_execution_stats();
            // Convert to JobInfo for compatibility
            core::JobInfo job_info;
            job_info.job_id = execution_stats.pipeline_id;
            job_info.status = static_cast<core::JobStatus>(execution_stats.status);
            job_info.total_records = execution_stats.total_records_processed;
            job_info.processed_records = execution_stats.successful_records;
            job_info.error_records = execution_stats.failed_records;
            return job_info;
        });
    };

    // Start parallel processing
    job_futures.push_back(process_source("patients_complex.csv", "job_001"));
    job_futures.push_back(process_source("lab_results.json", "job_002"));

    // Wait for all jobs to complete
    std::vector<core::JobInfo> results;
    for (auto& future : job_futures) {
        results.push_back(future.get());
    }

    // Verify all jobs completed successfully
    for (const auto& result : results) {
        EXPECT_EQ(result.status, core::JobStatus::Completed);
        EXPECT_GT(result.processed_records, 0);
    }
}

// Test complex transformation chains with vocabulary mapping
TEST_F(ComplexETLWorkflowTest, TestComplexTransformationChains) {
    // Create transformation chain for patient data
    auto chain = std::make_unique<transform::TransformationChain>();

    // Add date transformation
    auto date_transform = std::make_unique<transform::DateTransformation>();
    YAML::Node date_config;
    date_config["input_format"] = "%Y-%m-%d";
    date_config["output_format"] = "%Y-%m-%d %H:%M:%S";
    date_config["add_time"] = true;
    date_transform->configure(date_config);
    chain->add_transformation(std::move(date_transform));

    // Add vocabulary mapping
    auto vocab_transform = std::make_unique<transform::VocabularyTransformation>(*vocabulary_service_);
    YAML::Node vocab_config;
    vocab_config["vocabulary_name"] = "Gender";
    vocab_config["default_concept_id"] = 0;
    vocab_transform->configure(vocab_config);
    chain->add_transformation(std::move(vocab_transform));

    // Test the chain
    core::ProcessingContext context;

    // Test date transformation
    std::any date_input = std::string("2024-01-15");
    auto date_result = chain->apply(date_input, context);
    EXPECT_TRUE(date_result.has_value());

    // Test vocabulary mapping
    std::any gender_input = std::string("Male");
    auto gender_result = chain->apply(gender_input, context);
    EXPECT_TRUE(gender_result.has_value());
    if (gender_result.type() == typeid(int)) {
        EXPECT_EQ(std::any_cast<int>(gender_result), 8507); // Male concept ID
    }
}

// Test error handling and recovery in complex workflows
TEST_F(ComplexETLWorkflowTest, TestErrorHandlingAndRecovery) {
    // Create pipeline with intentional errors
    core::PipelineConfig config;
    config.batch_size = 10;
    config.stop_on_error = false;
    config.error_threshold = 0.5; // Allow up to 50% errors

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Create test data with some invalid records
    auto invalid_data_file = test_data_dir_ / "invalid_data.csv";
    std::ofstream file(invalid_data_file);
    file << "id,value,date\n";
    file << "1,100,2024-01-15\n";
    file << "2,invalid_number,2024-01-16\n"; // Invalid numeric value
    file << "3,200,invalid_date\n"; // Invalid date
    file << "4,300,2024-01-17\n";
    file.close();

    // Set up extractor
    auto extractor = std::make_unique<extract::CsvExtractor>();
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["filepath"] = invalid_data_file.string();

    core::ProcessingContext context;
    extractor->initialize(extract_config, context);
    // Use PipelineBuilder instead of direct set methods
    auto builder = core::PipelineBuilder();
    builder.with_extractor(std::move(extractor));
    pipeline = builder.build();

    // Set up transformer with strict validation
    auto transformer = std::make_unique<transform::TransformationEngine>();
    
    // Set up loader
    auto loader = std::make_unique<load::DatabaseLoader>(
        createConnection("cdm"), load::DatabaseLoaderOptions{});
    
    // Use PipelineBuilder to add all components
    auto builder = core::PipelineBuilder();
    builder.with_transformer(std::move(transformer))
           .with_loader(std::move(loader));
    pipeline = builder.build();

    // Execute pipeline
    auto job = pipeline->start("error_handling_test");
    auto result = job.get();

    // Verify partial success
    EXPECT_EQ(result.status, core::JobStatus::Completed);
    EXPECT_EQ(result.total_records, 4);
    EXPECT_EQ(result.processed_records, 2); // Only valid records
    EXPECT_EQ(result.error_records, 2); // Invalid records
}

// Test checkpoint and recovery functionality
TEST_F(ComplexETLWorkflowTest, TestCheckpointAndRecovery) {
    // Configure pipeline with checkpointing
    core::PipelineConfig config;
    config.batch_size = 2;
    config.checkpoint_interval = std::chrono::seconds(1);
    config.checkpoint_dir = (test_data_dir_ / "checkpoints").string();
    std::filesystem::create_directories(config.checkpoint_dir);

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Create large test dataset
    auto large_file = test_data_dir_ / "large_dataset.csv";
    std::ofstream file(large_file);
    file << "id,value\n";
    for (int i = 1; i <= 100; ++i) {
        file << i << "," << i * 10 << "\n";
    }
    file.close();

    // Set up pipeline components
    auto extractor = std::make_unique<extract::CsvExtractor>();
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["filepath"] = large_file.string();

    core::ProcessingContext context;
    extractor->initialize(extract_config, context);
    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::make_unique<transform::TransformationEngine>());
    pipeline->set_loader(std::make_unique<load::DatabaseLoader>(
        createConnection("cdm"), load::DatabaseLoaderOptions{}));

    // Start pipeline and interrupt it
    auto future = pipeline->start("checkpoint_test");

    // Let it process some records
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // Simulate interruption
    pipeline->pause();
    pipeline->stop();

    // Get partial result
    auto partial_result = future.get();
    EXPECT_GT(partial_result.processed_records, 0);
    EXPECT_LT(partial_result.processed_records, 100);

    // Verify checkpoint was created
    EXPECT_TRUE(std::filesystem::exists(config.checkpoint_dir));

    // Create new pipeline and resume from checkpoint
    auto resumed_pipeline = std::make_unique<core::ETLPipeline>(config);
    // Pipeline would load checkpoint and resume processing
    // (Implementation detail: actual checkpoint loading would be in the pipeline)
}

// Test end-to-end workflow with all components
TEST_F(ComplexETLWorkflowTest, TestEndToEndWorkflow) {
    // This test simulates a complete ETL workflow from multiple sources
    // through complex transformations to the final OMOP CDM tables

    auto config_manager = std::make_shared<common::ConfigurationManager>();
    auto pipeline_manager = std::make_shared<core::PipelineManager>();

    // Configure ETL service
    auto etl_service = std::make_shared<service::ETLService>(config_manager, pipeline_manager);

    // Track job results
    std::vector<std::string> job_ids;

    // Process patient demographics
    service::ETLJobRequest patient_request;
    patient_request.name = "Patient Demographics Import";
    patient_request.source_table = "patients_complex.csv";
    patient_request.target_table = "person";
    patient_request.extractor_type = "csv";
    patient_request.loader_type = "omop_database";
    patient_request.extractor_config["filepath"] = (test_data_dir_ / "patients_complex.csv").string();

    auto patient_job_id = etl_service->create_job(patient_request);
    job_ids.push_back(patient_job_id);

    // Process lab results
    service::ETLJobRequest lab_request;
    lab_request.name = "Lab Results Import";
    lab_request.source_table = "lab_results.json";
    lab_request.target_table = "measurement";
    lab_request.extractor_type = "json";
    lab_request.loader_type = "omop_database";
    lab_request.extractor_config["filepath"] = (test_data_dir_ / "lab_results.json").string();
    lab_request.extractor_config["root_path"] = "results";

    auto lab_job_id = etl_service->create_job(lab_request);
    job_ids.push_back(lab_job_id);

    // Process clinical notes
    service::ETLJobRequest notes_request;
    notes_request.name = "Clinical Notes Import";
    notes_request.source_table = "clinical_notes.jsonl";
    notes_request.target_table = "note";
    notes_request.extractor_type = "jsonl";
    notes_request.loader_type = "omop_database";
    notes_request.extractor_config["filepath"] = (test_data_dir_ / "clinical_notes.jsonl").string();

    auto notes_job_id = etl_service->create_job(notes_request);
    job_ids.push_back(notes_job_id);

    // Wait for all jobs to complete
    bool all_successful = true;
    for (const auto& job_id : job_ids) {
        // Poll for completion (in real implementation, would use callbacks)
        while (true) {
            auto result = etl_service->get_job_result(job_id);
            if (result.has_value()) {
                if (result->status == core::JobStatus::Completed) {
                    EXPECT_GT(result->processed_records, 0);
                    break;
                } else if (result->status == core::JobStatus::Failed) {
                    all_successful = false;
                    break;
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    EXPECT_TRUE(all_successful);

    // Verify data was loaded correctly
    auto conn = createConnection("cdm");

    // Check person table
    auto person_result = conn->execute_query("SELECT COUNT(*) FROM person");
    EXPECT_TRUE(person_result->next());

    // Check measurement table
    auto measurement_result = conn->execute_query("SELECT COUNT(*) FROM measurement");
    EXPECT_TRUE(measurement_result->next());

    // Check note table
    auto note_result = conn->execute_query("SELECT COUNT(*) FROM note");
    EXPECT_TRUE(note_result->next());
}

} // namespace omop::test::integration