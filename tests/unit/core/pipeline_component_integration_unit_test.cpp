// File: tests/unit/core/pipeline_integration_test.cpp

#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include <atomic>
#include <chrono>

namespace omop::core {

class IntegrationExtractor : public IExtractor {
private:
    std::atomic<size_t> batch_count{0};
    size_t total_batches = 3;
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        if (batch_count < total_batches) {
            for (size_t i = 0; i < batch_size; ++i) {
                Record r;
                r.setField("batch", static_cast<int>(batch_count.load()));
                r.setField("index", static_cast<int>(i));
                batch.addRecord(r);
            }
            batch_count++;
        }
        return batch;
    }
    
    bool has_more_data() const override { return batch_count < total_batches; }
    std::string get_type() const override { return "integration_extractor"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"batches_extracted", batch_count.load()}};
    }
};

class IntegrationTransformer : public ITransformer {
private:
    std::atomic<size_t> transform_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    std::optional<Record> transform(const Record& record, ProcessingContext&) override {
        Record r = record;
        r.setField("transformed", true);
        transform_count++;
        return r;
    }
    
    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        RecordBatch result;
        for (const auto& record : batch.getRecords()) {
            auto transformed = transform(record, context);
            if (transformed) {
                result.addRecord(*transformed);
            }
        }
        return result;
    }
    
    std::string get_type() const override { return "integration_transformer"; }
    omop::common::ValidationResult validate(const Record&) const override { return omop::common::ValidationResult(); }
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_transformed", transform_count.load()}};
    }
};

class IntegrationLoader : public ILoader {
private:
    std::atomic<size_t> load_count{0};
    std::atomic<size_t> commit_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    bool load(const Record&, ProcessingContext&) override {
        load_count++;
        return true;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        for (const auto& record : batch.getRecords()) {
            load(record, context);
        }
        return batch.size();
    }
    
    void commit(ProcessingContext&) override { commit_count++; }
    void rollback(ProcessingContext&) override {}
    std::string get_type() const override { return "integration_loader"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"records_loaded", load_count.load()},
            {"commits", commit_count.load()}
        };
    }
    
    size_t get_load_count() const { return load_count; }
    size_t get_commit_count() const { return commit_count; }
};

class PipelineIntegrationTest : public ::testing::Test {
protected:
    std::unique_ptr<ETLPipeline> pipeline;
    
    void SetUp() override {
        PipelineConfig config;
        config.batch_size = 10;
        config.commit_interval = 20;
        config.error_threshold = 0.1;
        pipeline = std::make_unique<ETLPipeline>(config);
    }
};

TEST_F(PipelineIntegrationTest, EndToEndExecution) {
    auto extractor = std::make_unique<IntegrationExtractor>();
    auto transformer = std::make_unique<IntegrationTransformer>();
    auto loader = std::make_unique<IntegrationLoader>();
    
    auto loader_ptr = loader.get();
    
    // Use PipelineBuilder instead of direct set methods
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(extractor))
           .with_transformer(std::move(transformer))
           .with_loader(std::move(loader));
    
    pipeline = builder.build();
    
    pipeline->start();
    auto execution_stats = pipeline->get_execution_stats();
    
    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);
    EXPECT_EQ(loader_ptr->get_load_count(), 30); // 3 batches * 10 records
    EXPECT_GT(loader_ptr->get_commit_count(), 0); // At least one commit
}

TEST_F(PipelineIntegrationTest, ErrorThresholdHandling) {
    // Test pipeline behavior when error threshold is exceeded
    class FailingTransformer : public IntegrationTransformer {
    private:
        std::atomic<size_t> count{0};
        
    public:
        std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
            if (++count % 3 == 0) {
                context.increment_errors();
                return std::nullopt; // Fail every 3rd record
            }
            return IntegrationTransformer::transform(record, context);
        }
    };
    
    // Create a new pipeline with the correct error threshold config
    PipelineConfig config;
    config.batch_size = 10;
    config.commit_interval = 20;
    config.error_threshold = 0.2; // 20% error threshold
    config.stop_on_error = true;
    
    auto error_pipeline = std::make_unique<ETLPipeline>(config);
    
    // Use PipelineBuilder instead of direct set methods
    auto builder = PipelineBuilder();
    builder.with_extractor(std::make_unique<IntegrationExtractor>())
           .with_transformer(std::make_unique<FailingTransformer>())
           .with_loader(std::make_unique<IntegrationLoader>());
    
    error_pipeline = builder.build();
    
    error_pipeline->start();
    auto execution_stats = error_pipeline->get_execution_stats();
    
    // Pipeline should fail due to error threshold
    EXPECT_EQ(execution_stats.status, PipelineStatus::Failed);
}

} // namespace omop::core