/**
 * @file test_pipeline.cpp
 * @brief Unit tests for ETL pipeline components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <fstream>
#include <filesystem>
#include <mutex>
#include <atomic>

using namespace omop::core;
using namespace testing;

// Type aliases to help with MOCK_METHOD syntax
using ConfigMap = std::unordered_map<std::string, std::any>;
using StatsMap = std::unordered_map<std::string, std::any>;

// Mock implementations for testing
class MockExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockTransformer : public ITransformer {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(std::optional<Record>, transform, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, transform_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(omop::common::ValidationResult, validate, (const Record&), (const, override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockLoader : public ILoader {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(bool, load, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(size_t, load_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(void, commit, (ProcessingContext&), (override));
    MOCK_METHOD(void, rollback, (ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

// Mock extractor that produces test data
class TestExtractor : public IExtractor {
private:
    size_t records_produced = 0;
    size_t total_records = 10;

public:
    void initialize(const ConfigMap& config, ProcessingContext& context) override {}

    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        size_t to_produce = std::min(batch_size, total_records - records_produced);

        for (size_t i = 0; i < to_produce; ++i) {
            Record r;
            r.setField("id", static_cast<int>(records_produced + i));
            r.setField("value", static_cast<int>((records_produced + i) * 10));
            batch.addRecord(r);
        }

        records_produced += to_produce;
        return batch;
    }

    bool has_more_data() const override {
        return records_produced < total_records;
    }

    std::string get_type() const override { return "test"; }
    void finalize(ProcessingContext& context) override {}
    StatsMap get_statistics() const override {
        return {{"records_produced", records_produced}};
    }
};

// Mock transformer that passes through
class PassthroughTransformer : public ITransformer {
public:
    void initialize(const ConfigMap& config, ProcessingContext& context) override {}

    std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
        return record;
    }

    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        return batch;
    }

    std::string get_type() const override { return "passthrough"; }

    omop::common::ValidationResult validate(const Record& record) const override {
        return omop::common::ValidationResult();
    }

    StatsMap get_statistics() const override {
        return {};
    }
};

// Mock loader that counts records
class CountingLoader : public ILoader {
private:
    std::atomic<size_t> loaded_count{0};

public:
    void initialize(const ConfigMap& config, ProcessingContext& context) override {}

    bool load(const Record& record, ProcessingContext& context) override {
        loaded_count++;
        return true;
    }

    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        loaded_count += batch.size();
        return batch.size();
    }

    void commit(ProcessingContext& context) override {}
    void rollback(ProcessingContext& context) override {}
    std::string get_type() const override { return "counting"; }
    void finalize(ProcessingContext& context) override {}

    StatsMap get_statistics() const override {
        return {{"loaded_count", loaded_count.load()}};
    }

    size_t get_loaded_count() const { return loaded_count; }
};

// JobInfo tests
class JobInfoTest : public ::testing::Test {
protected:
    JobInfo info;

    void SetUp() override {
        info.job_id = "test-job";
        info.job_name = "Test Job";
        info.status = JobStatus::Running;
        info.start_time = std::chrono::system_clock::now();
        info.total_records = 1000;
        info.processed_records = 750;
        info.error_records = 50;
    }
};

// Test verifies that JobInfo calculates duration correctly for running jobs
TEST_F(JobInfoTest, DurationRunning) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    auto duration = info.duration();
    EXPECT_GE(duration.count(), 0.1);
}

// Test verifies that JobInfo calculates duration correctly for completed jobs
TEST_F(JobInfoTest, DurationCompleted) {
    info.status = JobStatus::Completed;
    info.end_time = info.start_time + std::chrono::seconds(10);
    auto duration = info.duration();
    EXPECT_DOUBLE_EQ(duration.count(), 10.0);
}

// Test verifies that JobInfo calculates progress percentage correctly
TEST_F(JobInfoTest, Progress) {
    EXPECT_DOUBLE_EQ(info.progress(), 75.0);

    info.processed_records = 1000;
    EXPECT_DOUBLE_EQ(info.progress(), 100.0);

    info.total_records = 0;
    EXPECT_DOUBLE_EQ(info.progress(), 0.0);
}

// Test verifies that JobInfo calculates error rate correctly
TEST_F(JobInfoTest, ErrorRate) {
    EXPECT_DOUBLE_EQ(info.error_rate(), 50.0 / 750.0);

    info.processed_records = 0;
    EXPECT_DOUBLE_EQ(info.error_rate(), 0.0);
}

// ETLPipeline tests
class ETLPipelineTest : public ::testing::Test {
protected:
    std::unique_ptr<ETLPipeline> pipeline;
    std::unique_ptr<MockExtractor> extractor;
    std::unique_ptr<MockTransformer> transformer;
    std::unique_ptr<MockLoader> loader;

    void SetUp() override {
        PipelineConfig config;
        config.batch_size = 10;
        config.queue_size = 100;
        config.commit_interval = 50;
        pipeline = std::make_unique<ETLPipeline>(config);

        extractor = std::make_unique<MockExtractor>();
        transformer = std::make_unique<MockTransformer>();
        loader = std::make_unique<MockLoader>();
    }

    void TearDown() override {
        if (pipeline && pipeline->get_status() == PipelineStatus::Running) {
            pipeline->stop();
        }
    }
};

// Test verifies that ETLPipeline constructor initializes with correct default status
TEST_F(ETLPipelineTest, Construction) {
    EXPECT_EQ(pipeline->get_status(), PipelineStatus::Created);
    auto execution_stats = pipeline->get_execution_stats();
    EXPECT_EQ(execution_stats.status, PipelineStatus::Created);
}

// Test verifies that ETLPipeline can add pipeline stages
TEST_F(ETLPipelineTest, AddStages) {
    // Create and add extract stage
    auto extract_stage = std::make_unique<ExtractStage>(std::move(extractor));
    EXPECT_TRUE(pipeline->add_stage(std::move(extract_stage)));
    
    // Create and add transform stage
    auto transform_stage = std::make_unique<TransformStage>(std::move(transformer));
    EXPECT_TRUE(pipeline->add_stage(std::move(transform_stage)));
    
    // Create and add load stage
    auto load_stage = std::make_unique<LoadStage>(std::move(loader));
    EXPECT_TRUE(pipeline->add_stage(std::move(load_stage)));

    // Components are moved, so pointers should be null
    EXPECT_EQ(extractor, nullptr);
    EXPECT_EQ(transformer, nullptr);
    EXPECT_EQ(loader, nullptr);
    
    // Check that stages were added
    auto stages = pipeline->get_stages();
    EXPECT_EQ(stages.size(), 3);
    EXPECT_TRUE(std::find(stages.begin(), stages.end(), PipelineStage::Extract) != stages.end());
    EXPECT_TRUE(std::find(stages.begin(), stages.end(), PipelineStage::Transform) != stages.end());
    EXPECT_TRUE(std::find(stages.begin(), stages.end(), PipelineStage::Load) != stages.end());
}

// Test verifies that ETLPipeline throws exception when starting without required stages
TEST_F(ETLPipelineTest, StartWithoutStagesThrows) {
    // Initialize pipeline first
    ETLPipelineConfig config;
    config.pipeline_id = "test-pipeline";
    config.pipeline_name = "Test Pipeline";
    EXPECT_TRUE(pipeline->initialize(config));
    
    // Starting without stages should throw
    EXPECT_THROW(pipeline->start(), std::runtime_error);
}

// Test verifies that ETLPipeline can add pre and post processors
TEST_F(ETLPipelineTest, AddProcessors) {
    int pre_count = 0;
    int post_count = 0;

    pipeline->add_pre_processor([&pre_count](RecordBatch& batch, ProcessingContext& ctx) {
        pre_count++;
    });

    pipeline->add_post_processor([&post_count](RecordBatch& batch, ProcessingContext& ctx) {
        post_count++;
    });

    // Processors are stored but not executed until pipeline runs
    EXPECT_EQ(pre_count, 0);
    EXPECT_EQ(post_count, 0);
}

// Test verifies that ETLPipeline can set progress and error callbacks
TEST_F(ETLPipelineTest, SetCallbacks) {
    bool progress_called = false;
    bool error_called = false;

    pipeline->register_progress_callback([&progress_called](const PipelineExecutionStats& stats) {
        progress_called = true;
    });

    pipeline->register_error_callback([&error_called](const std::string& msg, const PipelineExecutionStats& stats) {
        error_called = true;
    });

    // Callbacks are stored but not executed until pipeline runs
    EXPECT_FALSE(progress_called);
    EXPECT_FALSE(error_called);
}

// Test verifies that ETLPipeline can be paused and resumed correctly
TEST_F(ETLPipelineTest, PauseAndResume) {
    pipeline->pause();
    EXPECT_EQ(pipeline->get_status(), PipelineStatus::Paused);

    pipeline->resume();
    EXPECT_NE(pipeline->get_status(), PipelineStatus::Paused);
}

// PipelineBuilder tests
class PipelineBuilderTest : public ::testing::Test {
protected:
    PipelineBuilder builder;

    void SetUp() override {
        // Create temporary config file for testing
        std::filesystem::create_directories("test_config");
        std::ofstream config_file("test_config/pipeline.yaml");
        config_file << "pipeline:\n"
                   << "  batch_size: 50\n"
                   << "  queue_size: 200\n"
                   << "  commit_interval: 100\n"
                   << "  error_threshold: 0.05\n"
                   << "  stop_on_error: false\n"
                   << "  validate_records: true\n"
                   << "  checkpoint_interval: 60\n"
                   << "  checkpoint_dir: /tmp/checkpoints\n"
                   << "extractor:\n"
                   << "  type: csv\n"
                   << "  file: test.csv\n"
                   << "transformer:\n"
                   << "  type: mapping\n"
                   << "  mapping_file: mappings.yaml\n"
                   << "loader:\n"
                   << "  type: database\n"
                   << "  connection_string: test_db\n";
        config_file.close();
    }

    void TearDown() override {
        std::filesystem::remove_all("test_config");
    }
};

// Test verifies that PipelineBuilder can create pipeline with provided configuration
TEST_F(PipelineBuilderTest, WithConfig) {
    PipelineConfig config;
    config.batch_size = 100;
    config.queue_size = 1000;

    auto pipeline = builder.with_config(config).build();
    EXPECT_NE(pipeline, nullptr);
}

// Test verifies that PipelineBuilder handles config file parsing correctly
TEST_F(PipelineBuilderTest, WithConfigFile) {
    // Note: This will fail because component factories aren't registered
    // But it tests the config file parsing
    EXPECT_THROW(
        builder.with_config_file("test_config/pipeline.yaml").build(),
        omop::common::ConfigurationException
    );
}

// Test verifies that PipelineBuilder throws exception for non-existent config files
TEST_F(PipelineBuilderTest, WithNonExistentConfigFileThrows) {
    EXPECT_THROW(
        builder.with_config_file("nonexistent.yaml"),
        omop::common::ConfigurationException
    );
}

// Test verifies that PipelineBuilder can create pipeline with custom components
TEST_F(PipelineBuilderTest, WithCustomComponents) {
    auto pipeline = builder
        .with_config(PipelineConfig{})
        .with_extractor(std::make_unique<TestExtractor>())
        .with_transformer(std::make_unique<PassthroughTransformer>())
        .with_loader(std::make_unique<CountingLoader>())
        .build();

    EXPECT_NE(pipeline, nullptr);
}

// Test verifies that PipelineBuilder can add pre and post processors
TEST_F(PipelineBuilderTest, WithProcessors) {
    int pre_called = 0;
    int post_called = 0;

    auto pipeline = builder
        .with_config(PipelineConfig{})
        .with_extractor(std::make_unique<TestExtractor>())
        .with_transformer(std::make_unique<PassthroughTransformer>())
        .with_loader(std::make_unique<CountingLoader>())
        .with_pre_processor([&pre_called](RecordBatch& batch, ProcessingContext& ctx) {
            pre_called++;
        })
        .with_post_processor([&post_called](RecordBatch& batch, ProcessingContext& ctx) {
            post_called++;
        })
        .build();

    EXPECT_NE(pipeline, nullptr);
}

// Test verifies that PipelineBuilder can set progress and error callbacks
TEST_F(PipelineBuilderTest, WithCallbacks) {
    bool progress_set = false;
    bool error_set = false;

    auto pipeline = builder
        .with_config(PipelineConfig{})
        .with_extractor(std::make_unique<TestExtractor>())
        .with_transformer(std::make_unique<PassthroughTransformer>())
        .with_loader(std::make_unique<CountingLoader>())
        .with_progress_callback([&progress_set](const JobInfo& info) {
            progress_set = true;
        })
        .with_error_callback([&error_set](const std::string& msg, const std::exception& e) {
            error_set = true;
        })
        .build();

    EXPECT_NE(pipeline, nullptr);
}

// Test verifies that PipelineBuilder validation works correctly for different scenarios
TEST_F(PipelineBuilderTest, BuilderValidation) {
    // Build without initializing succeeds but creates empty pipeline
    PipelineBuilder empty_builder;
    auto empty_pipeline = empty_builder.build();
    EXPECT_NE(empty_pipeline, nullptr);

    // Build without components is OK (will fail at runtime)
    auto pipeline = builder.with_config(PipelineConfig{}).build();
    EXPECT_NE(pipeline, nullptr);
}

// Test verifies that PipelineBuilder throws exception when provided with null components
TEST_F(PipelineBuilderTest, WithNullComponentsThrows) {
    builder.with_config(PipelineConfig{});

    EXPECT_THROW(builder.with_extractor(nullptr), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_transformer(nullptr), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_loader(nullptr), omop::common::ConfigurationException);
}

// Test verifies that PipelineBuilder throws exception when provided with empty component types
TEST_F(PipelineBuilderTest, WithEmptyTypeThrows) {
    builder.with_config(PipelineConfig{});

    ConfigMap params;
    EXPECT_THROW(builder.with_extractor("", params), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_transformer("", params), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_loader("", params), omop::common::ConfigurationException);
}

// Test verifies that PipelineBuilder throws exception when provided with null callbacks
TEST_F(PipelineBuilderTest, WithNullCallbacksThrows) {
    builder.with_config(PipelineConfig{});

    EXPECT_THROW(builder.with_progress_callback(nullptr), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_error_callback(nullptr), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_pre_processor(nullptr), omop::common::ConfigurationException);
    EXPECT_THROW(builder.with_post_processor(nullptr), omop::common::ConfigurationException);
}

// PipelineManager tests
class PipelineManagerTest : public ::testing::Test {
protected:
    std::unique_ptr<PipelineManager> manager;

    void SetUp() override {
        manager = std::make_unique<PipelineManager>(2);
    }

    void TearDown() override {
        if (manager) {
            manager->shutdown(false);
        }
    }

    std::unique_ptr<ETLPipeline> create_test_pipeline() {
        PipelineConfig config;
        config.batch_size = 5;
        config.max_parallel_batches = 2;
        config.queue_size = 100;
        config.commit_interval = 5;

        return PipelineBuilder()
            .with_config(config)
            .with_extractor(std::make_unique<TestExtractor>())
            .with_transformer(std::make_unique<PassthroughTransformer>())
            .with_loader(std::make_unique<CountingLoader>())
            .build();
    }
};

// Test verifies that PipelineManager constructor initializes with empty job list
TEST_F(PipelineManagerTest, Construction) {
    PipelineManager mgr(4);
    EXPECT_TRUE(mgr.get_all_jobs().empty());
}

// Test verifies that PipelineManager can submit jobs and track their completion
TEST_F(PipelineManagerTest, SubmitJob) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    EXPECT_FALSE(job_id.empty());

    // Wait for job to complete with timeout
    bool completed = manager->wait_for_job(job_id, 10000); // 10 second timeout
    EXPECT_TRUE(completed);

    auto status = manager->get_job_status(job_id);
    ASSERT_TRUE(status.has_value());
    // Job should be completed or failed
    EXPECT_TRUE(status.value() == JobStatus::Completed ||
                status.value() == JobStatus::Failed ||
                status.value() == JobStatus::Cancelled);
}

// Test verifies that PipelineManager throws exception when submitting null pipeline
TEST_F(PipelineManagerTest, SubmitNullPipelineThrows) {
    EXPECT_THROW(manager->submit_job("test", nullptr), omop::common::ConfigurationException);
}

// Test verifies that PipelineManager can retrieve job information by ID
TEST_F(PipelineManagerTest, GetJobInfo) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    auto info = manager->get_job_info(job_id);
    ASSERT_TRUE(info.has_value());
    EXPECT_EQ(info->job_id, job_id);
    EXPECT_EQ(info->job_name, "test-job");
}

// Test verifies that PipelineManager returns empty optional for non-existent jobs
TEST_F(PipelineManagerTest, GetNonExistentJob) {
    auto status = manager->get_job_status("nonexistent");
    EXPECT_FALSE(status.has_value());

    auto info = manager->get_job_info("nonexistent");
    EXPECT_FALSE(info.has_value());
}

// Test verifies that PipelineManager can cancel running jobs
TEST_F(PipelineManagerTest, CancelJob) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    // Try to cancel immediately (may succeed or fail depending on timing)
    [[maybe_unused]] bool result = manager->cancel_job(job_id);

    // Wait for job to complete with timeout
    bool completed = manager->wait_for_job(job_id, 10000); // 10 second timeout
    EXPECT_TRUE(completed);

    auto status = manager->get_job_status(job_id);
    if (status.has_value()) {
        // Job may complete successfully or be cancelled depending on timing
        EXPECT_TRUE(status.value() == JobStatus::Cancelled ||
                   status.value() == JobStatus::Completed ||
                   status.value() == JobStatus::Failed);
    }
}

// Test verifies that PipelineManager returns false when canceling non-existent job
TEST_F(PipelineManagerTest, CancelNonExistentJob) {
    bool result = manager->cancel_job("nonexistent");
    EXPECT_FALSE(result);
}

// Test verifies that PipelineManager returns all submitted jobs
TEST_F(PipelineManagerTest, GetAllJobs) {
    auto pipeline1 = create_test_pipeline();
    auto pipeline2 = create_test_pipeline();

    auto job_id1 = manager->submit_job("job1", std::move(pipeline1));
    auto job_id2 = manager->submit_job("job2", std::move(pipeline2));

    auto all_jobs = manager->get_all_jobs();
    EXPECT_EQ(all_jobs.size(), 2);

    std::set<std::string> job_ids;
    for (const auto& job : all_jobs) {
        job_ids.insert(job.job_id);
    }

    EXPECT_TRUE(job_ids.count(job_id1) > 0);
    EXPECT_TRUE(job_ids.count(job_id2) > 0);
}

// Test verifies that PipelineManager can retrieve active jobs from all jobs
TEST_F(PipelineManagerTest, GetActiveJobs) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    auto all_jobs = manager->get_all_jobs();
    EXPECT_GE(all_jobs.size(), 1);

    bool found = false;
    for (const auto& job : all_jobs) {
        if (job.job_id == job_id) {
            found = true;
            break;
        }
    }
    EXPECT_TRUE(found);
}

// Test verifies that PipelineManager maintains correct job count
TEST_F(PipelineManagerTest, JobCount) {
    EXPECT_EQ(manager->get_all_jobs().size(), 0);

    auto pipeline1 = create_test_pipeline();
    auto pipeline2 = create_test_pipeline();

    manager->submit_job("job1", std::move(pipeline1));
    manager->submit_job("job2", std::move(pipeline2));

    EXPECT_EQ(manager->get_all_jobs().size(), 2);
}

// Test verifies that PipelineManager handles graceful shutdown correctly
TEST_F(PipelineManagerTest, Shutdown) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    // Graceful shutdown
    manager->shutdown(true);

    // After shutdown, the manager should be in shutdown state
    // Note: The actual implementation may still accept jobs after shutdown
    auto pipeline2 = create_test_pipeline();
    auto job_id2 = manager->submit_job("test-job2", std::move(pipeline2));
    // Just verify the call doesn't crash
    EXPECT_FALSE(job_id2.empty());
}

// Test verifies that PipelineManager handles force shutdown correctly
TEST_F(PipelineManagerTest, ForceShutdown) {
    auto pipeline = create_test_pipeline();
    auto job_id = manager->submit_job("test-job", std::move(pipeline));

    // Force shutdown
    manager->shutdown(false);

    // After shutdown, the manager should be in shutdown state
    // Note: The actual implementation may still accept jobs after shutdown
    auto pipeline2 = create_test_pipeline();
    auto job_id2 = manager->submit_job("test-job2", std::move(pipeline2));
    // Just verify the call doesn't crash
    EXPECT_FALSE(job_id2.empty());
}

// Test verifies that PipelineManager constructor allows zero thread count
TEST(PipelineManagerConstructorTest, ZeroThreadsAllowed) {
    // The actual implementation allows 0 threads
    PipelineManager manager(0);
    EXPECT_TRUE(manager.get_all_jobs().empty());
}

// Test verifies that PipelineManager constructor handles large thread counts
TEST(PipelineManagerConstructorTest, LargeThreadCount) {
    // Test that large thread counts work
    PipelineManager manager(100);
    EXPECT_TRUE(manager.get_all_jobs().empty());
}

// Additional pipeline tests from pipeline_test2.cpp
class AdditionalPipelineTest : public ::testing::Test {
protected:
    void SetUp() override {
        pipeline_ = std::make_unique<ETLPipeline>();
        
        // Create mock components
        mock_extractor_ = std::make_unique<MockExtractor>();
        mock_transformer_ = std::make_unique<MockTransformer>();
        mock_loader_ = std::make_unique<MockLoader>();
        
        // Store raw pointers for setting expectations
        extractor_ptr_ = mock_extractor_.get();
        transformer_ptr_ = mock_transformer_.get();
        loader_ptr_ = mock_loader_.get();
        
        // Set default expectations
        ON_CALL(*extractor_ptr_, get_type()).WillByDefault(Return("mock_extractor"));
        ON_CALL(*transformer_ptr_, get_type()).WillByDefault(Return("mock_transformer"));
        ON_CALL(*loader_ptr_, get_type()).WillByDefault(Return("mock_loader"));
    }
    
    std::unique_ptr<ETLPipeline> pipeline_;
    std::unique_ptr<MockExtractor> mock_extractor_;
    std::unique_ptr<MockTransformer> mock_transformer_;
    std::unique_ptr<MockLoader> mock_loader_;
    
    MockExtractor* extractor_ptr_;
    MockTransformer* transformer_ptr_;
    MockLoader* loader_ptr_;
};

// Test verifies that pipeline pre and post processors are executed in correct order
TEST_F(AdditionalPipelineTest, PrePostProcessors) {
    int pre_count = 0;
    int post_count = 0;
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior
    RecordBatch test_batch;
    for (int i = 0; i < 3; ++i) {
        Record record;
        record.setField("id", i);
        test_batch.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(test_batch));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillOnce(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch; // Pass through
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(3)); // All records loaded successfully
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1);
    
    // Set up finalization
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components
    pipeline_->set_extractor(std::move(mock_extractor_));
    pipeline_->set_transformer(std::move(mock_transformer_));
    pipeline_->set_loader(std::move(mock_loader_));
    
    // Add pre and post processors
    pipeline_->add_pre_processor([&pre_count](RecordBatch& batch, ProcessingContext& ctx) {
        pre_count++;
        // Modify the batch in pre-processing
        for (auto& record : batch) {
            record.setField("preprocessed", true);
        }
    });
    
    pipeline_->add_post_processor([&post_count](RecordBatch& batch, ProcessingContext& ctx) {
        post_count++;
        // Verify pre-processing was done
        for (const auto& record : batch) {
            EXPECT_TRUE(std::any_cast<bool>(record.getField("preprocessed")));
        }
    });
    
    // Start pipeline
    pipeline_->start();
    auto execution_stats = pipeline_->get_execution_stats();
    
    EXPECT_EQ(PipelineStatus::Completed, execution_stats.status);
    EXPECT_EQ(3, execution_stats.successful_records);
    EXPECT_EQ(1, pre_count);  // Called once for the batch
    EXPECT_EQ(1, post_count); // Called once for the batch
}

// Test verifies that pipeline progress callbacks are triggered during execution
TEST_F(AdditionalPipelineTest, ProgressCallback) {
    std::vector<double> progress_values;
    std::mutex progress_mutex;
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior with multiple batches
    RecordBatch batch1, batch2;
    for (int i = 0; i < 3; ++i) {
        Record record;
        record.setField("id", i);
        batch1.addRecord(record);
    }
    for (int i = 3; i < 6; ++i) {
        Record record;
        record.setField("id", i);
        batch2.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillRepeatedly(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch; // Pass through
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(3))
        .WillOnce(Return(3));
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1); // Only final commit for 6 records with interval 10000
    
    // Set up finalization
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components using PipelineBuilder
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(mock_extractor_))
           .with_transformer(std::move(mock_transformer_))
           .with_loader(std::move(mock_loader_));
    
    pipeline_ = builder.build();
    
    // Set progress callback
    pipeline_->register_progress_callback([&progress_values, &progress_mutex](const PipelineExecutionStats& stats) {
        std::lock_guard<std::mutex> lock(progress_mutex);
        double progress = stats.total_records_processed > 0 ? 
            (static_cast<double>(stats.successful_records) / stats.total_records_processed) : 0.0;
        progress_values.push_back(progress);
    });
    
    // Start pipeline
    pipeline_->start();
    auto execution_stats = pipeline_->get_execution_stats();
    
    EXPECT_EQ(PipelineStatus::Completed, execution_stats.status);
    EXPECT_EQ(6, execution_stats.successful_records);
    
    // Should have received progress updates
    EXPECT_GT(progress_values.size(), 0);
    
    // Progress should be monotonically increasing
    for (size_t i = 1; i < progress_values.size(); ++i) {
        EXPECT_GE(progress_values[i], progress_values[i-1]);
    }
}

// Test verifies that pipeline can be paused and resumed during execution
TEST_F(AdditionalPipelineTest, PauseAndResume) {
    std::atomic<bool> paused{false};
    std::atomic<int> pause_count{0};
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior
    RecordBatch test_batch;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_batch.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(test_batch));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillOnce(Invoke([&paused, &pause_count](const RecordBatch& batch, ProcessingContext&) {
            if (paused) {
                pause_count++;
                // Simulate some processing time while paused
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            return batch;
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(5));
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1);
    
    // Set up finalization
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components using PipelineBuilder
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(mock_extractor_))
           .with_transformer(std::move(mock_transformer_))
           .with_loader(std::move(mock_loader_));
    
    pipeline_ = builder.build();
    
    // Start pipeline
    pipeline_->start();
    
    // Pause after a short delay
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    pipeline_->pause();
    paused = true;
    
    // Wait a bit while paused
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Resume
    pipeline_->resume();
    paused = false;
    
    auto execution_stats = pipeline_->get_execution_stats();
    
    EXPECT_EQ(PipelineStatus::Completed, execution_stats.status);
    EXPECT_EQ(5, execution_stats.successful_records);
}

// Test verifies that pipeline can be stopped gracefully during execution
TEST_F(AdditionalPipelineTest, GracefulStop) {
    std::atomic<bool> should_stop{false};
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior that can be stopped
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillRepeatedly(Return(true));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillRepeatedly(Invoke([&should_stop](size_t, ProcessingContext&) {
            if (should_stop) {
                return RecordBatch(); // Empty batch
            }
            
            RecordBatch batch;
            Record r;
            r.setField("id", 1);
            batch.addRecord(r);
            
            // Allow some processing before stop
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            return batch;
        }));
    
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillRepeatedly(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch;
        }));
    
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillRepeatedly(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch.size();
        }));
    
    pipeline_->set_extractor(std::move(mock_extractor_));
    pipeline_->set_transformer(std::move(mock_transformer_));
    pipeline_->set_loader(std::move(mock_loader_));
    
    auto future = pipeline_->start("stop_test");
    
    // Let it process a bit
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Stop the pipeline
    should_stop = true;
    pipeline_->stop();
    
    auto job_info = future.get();
    
    // Should have stopped before completion
    EXPECT_TRUE(job_info.status == JobStatus::Cancelled ||
                job_info.status == JobStatus::Completed);
}

// Test verifies that pipeline stops when error threshold is exceeded
TEST_F(AdditionalPipelineTest, ErrorThresholdExceeded) {
    PipelineConfig config;
    config.error_threshold = 0.2; // 20% error threshold
    config.stop_on_error = true;
    
    ETLPipeline pipeline(config);
    
    // Create batch with some errors
    RecordBatch batch;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        r.setField("should_fail", i < 3); // First 3 records will fail
        batch.addRecord(r);
    }
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(batch));
    
    // Set up finalization expectations
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set up commit expectation for the error threshold test
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(AtMost(1));
    
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillOnce(Invoke([](const RecordBatch& batch, ProcessingContext& ctx) {
            RecordBatch result;
            for (const auto& record : batch) {
                if (record.getFieldAs<bool>("should_fail")) {
                    ctx.increment_errors();
                } else {
                    result.addRecord(record);
                }
            }
            return result;
        }));
    
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .Times(AtMost(1))
        .WillOnce(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch.size();
        }));
    
    pipeline.set_extractor(std::move(mock_extractor_));
    pipeline.set_transformer(std::move(mock_transformer_));
    pipeline.set_loader(std::move(mock_loader_));
    
    auto future = pipeline.start("error_threshold_test");
    auto job_info = future.get();
    
    // Should have failed due to error threshold
    EXPECT_EQ(JobStatus::Failed, job_info.status);
    EXPECT_EQ(3, job_info.error_records);
}

// Test verifies that multiple pipelines can run concurrently without interference
TEST_F(AdditionalPipelineTest, MultiplePipelinesConcurrent) {
    const int num_pipelines = 5;
    std::vector<std::unique_ptr<ETLPipeline>> pipelines;
    std::vector<std::future<JobInfo>> futures;
    
    for (int i = 0; i < num_pipelines; ++i) {
        auto pipeline = std::make_unique<ETLPipeline>();
        
        // Create simple mock components for each pipeline
        auto extractor = std::make_unique<MockExtractor>();
        auto transformer = std::make_unique<MockTransformer>();
        auto loader = std::make_unique<MockLoader>();
        
        RecordBatch batch;
        Record r;
        r.setField("pipeline_id", i);
        batch.addRecord(r);
        
        EXPECT_CALL(*extractor, initialize(_, _)).Times(1);
        EXPECT_CALL(*transformer, initialize(_, _)).Times(1);
        EXPECT_CALL(*loader, initialize(_, _)).Times(1);
        
        EXPECT_CALL(*extractor, has_more_data())
            .WillOnce(Return(true))
            .WillOnce(Return(false));
        
        EXPECT_CALL(*extractor, extract_batch(_, _))
            .WillOnce(Return(batch));
        
        EXPECT_CALL(*transformer, transform_batch(_, _))
            .WillOnce(Return(batch));
        
        EXPECT_CALL(*loader, load_batch(_, _))
            .WillOnce(Return(1));
            
        EXPECT_CALL(*extractor, finalize(_)).Times(1);
        EXPECT_CALL(*loader, finalize(_)).Times(1);
        
        pipeline->set_extractor(std::move(extractor));
        pipeline->set_transformer(std::move(transformer));
        pipeline->set_loader(std::move(loader));
        
        futures.push_back(pipeline->start("pipeline_" + std::to_string(i)));
        pipelines.push_back(std::move(pipeline));
    }
    
    // Wait for all pipelines to complete
    for (int i = 0; i < num_pipelines; ++i) {
        auto job_info = futures[i].get();
        EXPECT_EQ(JobStatus::Completed, job_info.status);
        EXPECT_EQ("pipeline_" + std::to_string(i), job_info.job_id);
    }
}
