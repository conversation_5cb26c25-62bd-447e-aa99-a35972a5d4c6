/**
 * @file validation_test.cpp
 * @brief Unit tests for validation framework with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/validation.h"
#include "common/logging.h"
#include <unordered_map>
#include <any>
#include <chrono>
#include <locale>
#include <iomanip>
#include <sstream>
#include <limits>

using namespace omop::common;

namespace omop::common::test {

// Test fixture for validation tests with UK locale setup
class ValidationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        engine_ = std::make_unique<BasicValidationEngine>();
        
        // UK-specific test data
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        nhs_number_ = "************";
        uk_postcode_ = "M60 1QD";
        
        setupUKTestRecords();
    }

    void TearDown() override {
        // BasicValidationEngine doesn't require explicit cleanup
    }

    void setupUKTestRecords() {
        // Valid UK NHS patient record
        valid_uk_record_["nhs_number"] = std::string("**********");
        valid_uk_record_["postcode"] = std::string("M60 1QD");
        valid_uk_record_["birth_date"] = std::string("15/01/1985");
        valid_uk_record_["gender"] = std::string("Male");
        valid_uk_record_["gp_practice_code"] = std::string("M12345");
        valid_uk_record_["treatment_cost"] = formatUKCurrency(125.50);
        
        // Invalid UK NHS patient record
        invalid_uk_record_["nhs_number"] = std::string("**********"); // Invalid checksum
        invalid_uk_record_["postcode"] = std::string("INVALID");      // Invalid postcode
        invalid_uk_record_["birth_date"] = std::string("32/01/1985"); // Invalid date
        invalid_uk_record_["gender"] = std::string("");               // Empty
        invalid_uk_record_["gp_practice_code"] = std::string("99999"); // Invalid format
        invalid_uk_record_["treatment_cost"] = std::string("invalid"); // Not currency
    }

    std::unique_ptr<BasicValidationEngine> engine_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string nhs_number_;
    std::string uk_postcode_;
    std::unordered_map<std::string, std::any> valid_uk_record_;
    std::unordered_map<std::string, std::any> invalid_uk_record_;

    // Helper to format UK currency
    std::string formatUKCurrency(double amount) {
        std::ostringstream oss;
        oss << uk_currency_symbol_ << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }
};

// Test basic validation result functionality
TEST_F(ValidationTest, BasicValidationResultFunctionality) {
    ValidationResult result;
    
    // Initially should be valid
    EXPECT_TRUE(result.is_valid());
    EXPECT_EQ(result.error_count(), 0u);
    EXPECT_TRUE(result.errors().empty());
    EXPECT_TRUE(result.warnings().empty());
    
    // Add an error
    result.add_error("nhs_number", "Invalid NHS number format", "nhs_validation");
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 1u);
    EXPECT_EQ(result.errors().size(), 1u);
    
    // Add a warning (shouldn't affect validity)
    result.add_warning("temperature", "Temperature in Fahrenheit, converting to Celsius", "unit_conversion");
    EXPECT_FALSE(result.is_valid()); // Still invalid due to error
    EXPECT_EQ(result.warnings().size(), 1u);
}

// Test that custom rule validates UK NHS numbers correctly
TEST_F(ValidationTest, CustomRuleValidatesUKNHSNumbersCorrectly) {
    auto nhs_validator = [](const std::any& value, const std::unordered_map<std::string, std::any>&) -> bool {
        try {
            std::string nhs_num = std::any_cast<std::string>(value);
            // Simple NHS number validation (would use ValidationUtils in real implementation)
            return nhs_num.length() >= 10 && nhs_num != "**********";
        } catch (...) {
            return false;
        }
    };
    
    auto rule = std::make_unique<CustomRule>("nhs_number", nhs_validator, "Invalid UK NHS number format");
    engine_->addRule(std::move(rule));
    
    auto result_valid = engine_->validateRecord(valid_uk_record_);
    EXPECT_TRUE(result_valid.is_valid());
    
    auto result_invalid = engine_->validateRecord(invalid_uk_record_);
    EXPECT_FALSE(result_invalid.is_valid());
    EXPECT_GT(result_invalid.errors().size(), 0u);
    EXPECT_TRUE(result_invalid.errors()[0].error_message.find("NHS number") != std::string::npos);
}

// Test that date range rule handles UK date formats  
TEST_F(ValidationTest, DateRangeRuleHandlesUKDateFormats) {
    // UK birth date should be between 1900 and today
    std::tm min_tm = {};
    min_tm.tm_sec = 0;
    min_tm.tm_min = 0;
    min_tm.tm_hour = 0;
    min_tm.tm_mday = 1;
    min_tm.tm_mon = 0;
    min_tm.tm_year = 0; // Jan 1, 1900
    auto min_date = std::chrono::system_clock::from_time_t(std::mktime(&min_tm));
    auto max_date = std::chrono::system_clock::now();
    
    auto rule = std::make_unique<DateRangeRule>("birth_date", min_date, max_date);
    engine_->addRule(std::move(rule));
    
    std::unordered_map<std::string, std::any> uk_record;
    std::tm birth_tm = {};
    birth_tm.tm_sec = 0;
    birth_tm.tm_min = 0;
    birth_tm.tm_hour = 0;
    birth_tm.tm_mday = 15;
    birth_tm.tm_mon = 0;
    birth_tm.tm_year = 85; // Jan 15, 1985
    uk_record["birth_date"] = std::chrono::system_clock::from_time_t(std::mktime(&birth_tm));
    
    auto result = engine_->validateRecord(uk_record);
    EXPECT_TRUE(result.is_valid());
}

// Test that in-list rule works with UK gender codes
TEST_F(ValidationTest, InListRuleWorksWithUKGenderCodes) {
    std::vector<std::string> uk_gender_codes = {"Male", "Female", "Other", "Not Specified"};
    auto rule = std::make_unique<InListRule<std::string>>("gender", uk_gender_codes);
    engine_->addRule(std::move(rule));
    
    auto result_valid = engine_->validateRecord(valid_uk_record_);
    EXPECT_TRUE(result_valid.is_valid());
    
    std::unordered_map<std::string, std::any> invalid_gender_record;
    invalid_gender_record["gender"] = std::string("Unknown");
    
    auto result_invalid = engine_->validateRecord(invalid_gender_record);
    EXPECT_FALSE(result_invalid.is_valid());
}

// Test that not null rule validates required UK fields
TEST_F(ValidationTest, NotNullRuleValidatesRequiredUKFields) {
    auto nhs_rule = std::make_unique<NotNullRule>("nhs_number");
    auto postcode_rule = std::make_unique<NotNullRule>("postcode");
    
    engine_->addRule(std::move(nhs_rule));
    engine_->addRule(std::move(postcode_rule));
    
    auto result_valid = engine_->validateRecord(valid_uk_record_);
    EXPECT_TRUE(result_valid.is_valid());
    
    std::unordered_map<std::string, std::any> null_record;
    null_record["nhs_number"] = std::any(); // null value
    null_record["postcode"] = std::string("M60 1QD");
    
    auto result_invalid = engine_->validateRecord(null_record);
    EXPECT_FALSE(result_invalid.is_valid());
    EXPECT_EQ(result_invalid.errors().size(), 1u);
}

// Test that numeric range rule validates UK healthcare costs
TEST_F(ValidationTest, NumericRangeRuleValidatesUKHealthcareCosts) {
    // UK NHS treatment costs should be between £0.01 and £10,000
    auto rule = std::make_unique<NumericRangeRule<double>>("treatment_cost_numeric", 0.01, 10000.0);
    engine_->addRule(std::move(rule));
    
    std::unordered_map<std::string, std::any> cost_record;
    cost_record["treatment_cost_numeric"] = 125.50;
    
    auto result_valid = engine_->validateRecord(cost_record);
    EXPECT_TRUE(result_valid.is_valid());
    
    cost_record["treatment_cost_numeric"] = 15000.0; // Above NHS limit
    auto result_invalid = engine_->validateRecord(cost_record);
    EXPECT_FALSE(result_invalid.is_valid());
}

// Test that regex rule validates UK postcode format
TEST_F(ValidationTest, RegexRuleValidatesUKPostcodeFormat) {
    // UK postcode pattern
    std::string uk_postcode_pattern = R"([A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2})";
    auto rule = std::make_unique<RegexRule>("postcode", uk_postcode_pattern);
    engine_->addRule(std::move(rule));
    
    auto result_valid = engine_->validateRecord(valid_uk_record_);
    EXPECT_TRUE(result_valid.is_valid());
    
    auto result_invalid = engine_->validateRecord(invalid_uk_record_);
    EXPECT_FALSE(result_invalid.is_valid());
}

// Test that validation engine handles batch processing of UK records
TEST_F(ValidationTest, ValidationEngineHandlesBatchProcessingOfUKRecords) {
    auto nhs_rule = std::make_unique<NotNullRule>("nhs_number");
    auto postcode_rule = std::make_unique<RegexRule>("postcode", R"([A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2})");
    
    engine_->addRule(std::move(nhs_rule));
    engine_->addRule(std::move(postcode_rule));
    
    std::vector<std::unordered_map<std::string, std::any>> uk_records = {
        valid_uk_record_,
        invalid_uk_record_,
        valid_uk_record_
    };
    
    auto result = engine_->validateBatch(uk_records, false);
    EXPECT_FALSE(result.is_valid());
    EXPECT_FALSE(result.errors().empty());
}

// Test that validation engine supports UK-specific validation types
TEST_F(ValidationTest, ValidationEngineSupportsUKSpecificValidationTypes) {
    // Enable specific validation types
    engine_->setValidationTypeEnabled(ValidationType::NOT_NULL, true);
    engine_->setValidationTypeEnabled(ValidationType::REGEX, true);
    engine_->setValidationTypeEnabled(ValidationType::CUSTOM, false);
    
    auto nhs_rule = std::make_unique<NotNullRule>("nhs_number");
    auto postcode_rule = std::make_unique<RegexRule>("postcode", R"([A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2})");
    
    engine_->addRule(std::move(nhs_rule));
    engine_->addRule(std::move(postcode_rule));
    
    EXPECT_EQ(engine_->getRuleCount(), 2u);
    
    auto result = engine_->validateRecord(valid_uk_record_);
    EXPECT_TRUE(result.is_valid());
}

// Test that validation result merge preserves UK error context
TEST_F(ValidationTest, ValidationResultMergePreservesUKErrorContext) {
    ValidationResult result1;
    result1.add_error("nhs_number", "NHS number validation failed", "nhs_validation");
    result1.add_error("postcode", "Invalid UK postcode format", "postcode_validation");
    
    ValidationResult result2;
    result2.add_error("gp_practice", "GP practice code not found", "gp_validation");
    result2.add_warning("temperature", "Temperature recorded in Fahrenheit, converting to Celsius", "temp_conversion");
    
    result1.merge(result2);
    
    EXPECT_FALSE(result1.is_valid());
    EXPECT_EQ(result1.errors().size(), 3u);
    EXPECT_EQ(result1.warnings().size(), 1u);
    
    // Verify UK-specific error messages are preserved
    bool found_nhs_error = false;
    bool found_postcode_error = false;
    bool found_gp_error = false;
    
    for (const auto& error : result1.errors()) {
        if (error.error_message.find("NHS number") != std::string::npos) found_nhs_error = true;
        if (error.error_message.find("UK postcode") != std::string::npos) found_postcode_error = true;
        if (error.error_message.find("GP practice") != std::string::npos) found_gp_error = true;
    }
    
    EXPECT_TRUE(found_nhs_error);
    EXPECT_TRUE(found_postcode_error);
    EXPECT_TRUE(found_gp_error);
}

// Test that validation result tracks UK healthcare processing metrics
TEST_F(ValidationTest, ValidationResultTracksUKHealthcareProcessingMetrics) {
    ValidationResult result;
    result.add_error("nhs_number", "NHS number ********** failed checksum validation", "nhs_checksum");
    result.add_error("postcode", "UK postcode 'INVALID' does not match Royal Mail format", "postcode_format");
    result.add_error("gp_practice", "GP practice code '99999' not found in NHS directory", "gp_lookup");
    
    result.add_warning("temperature", "Patient temperature 98.6°F converted to 37.0°C", "temp_conversion");
    result.add_warning("cost", "Treatment cost £125.50 exceeds typical NHS tariff", "cost_warning");
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.errors().size(), 3u);
    EXPECT_EQ(result.warnings().size(), 2u);
    
    // Verify UK healthcare-specific error content
    std::string all_errors;
    for (const auto& error : result.errors()) {
        all_errors += error.error_message + " ";
    }
    
    EXPECT_TRUE(all_errors.find("NHS number") != std::string::npos);
    EXPECT_TRUE(all_errors.find("Royal Mail") != std::string::npos);
    EXPECT_TRUE(all_errors.find("NHS directory") != std::string::npos);
    
    // Verify UK healthcare-specific warning content
    std::string all_warnings;
    for (const auto& warning : result.warnings()) {
        all_warnings += warning.warning_message + " ";
    }
    
    EXPECT_TRUE(all_warnings.find("°F") != std::string::npos);
    EXPECT_TRUE(all_warnings.find("°C") != std::string::npos);
    EXPECT_TRUE(all_warnings.find("£") != std::string::npos);
    EXPECT_TRUE(all_warnings.find("NHS tariff") != std::string::npos);
}

} // namespace omop::common::test